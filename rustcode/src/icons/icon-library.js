// RustCode Icon Library - Professional SVG Icons
// All icons are designed with consistent 16x16, 20x20, and 24x24 sizes
// Theme-aware with currentColor for automatic theming

export const Icons = {
    // File and Folder Icons
    file: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2 2C1.44772 2 1 2.44772 1 3V13C1 13.5523 1.44772 14 2 14H14C14.5523 14 15 13.5523 15 13V5.5L11.5 2H2Z" stroke="currentColor" stroke-width="1" fill="none"/>
        <path d="M11 2V5H14" stroke="currentColor" stroke-width="1" fill="none"/>
    </svg>`,

    folder: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 3C1 2.44772 1.44772 2 2 2H6L7.5 3.5H14C14.5523 3.5 15 3.94772 15 4.5V12C15 12.5523 14.5523 13 14 13H2C1.44772 13 1 12.5523 1 12V3Z" stroke="currentColor" stroke-width="1" fill="none"/>
    </svg>`,

    folderOpen: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 3C1 2.44772 1.44772 2 2 2H6L7.5 3.5H14C14.5523 3.5 15 3.94772 15 4.5V5H2.5L1 3Z" stroke="currentColor" stroke-width="1" fill="none"/>
        <path d="M2.5 5L3.5 12H14.5L15 5H2.5Z" stroke="currentColor" stroke-width="1" fill="none"/>
    </svg>`,

    // Navigation Icons
    chevronRight: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    chevronDown: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 6L8 10L12 6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    // Action Icons
    plus: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 3V13M3 8H13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    close: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 4L12 12M12 4L4 12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    refresh: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1.5 8C1.5 4.41015 4.41015 1.5 8 1.5C10.0902 1.5 11.9492 2.50098 13.0898 4.08984" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
        <path d="M14.5 8C14.5 11.5899 11.5899 14.5 8 14.5C5.90981 14.5 4.05078 13.499 2.91016 11.9102" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
        <path d="M11 2L13.5 4.5L11 7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M5 14L2.5 11.5L5 9" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    // Search Icons
    search: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="7" cy="7" r="4" stroke="currentColor" stroke-width="1.5"/>
        <path d="M11 11L14 14" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    ai: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 2L10 6H14L11 9L12 13L8 11L4 13L5 9L2 6H6L8 2Z" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <circle cx="8" cy="8" r="2" stroke="currentColor" stroke-width="1" fill="currentColor" opacity="0.3"/>
    </svg>`,

    send: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2 8L14 2L10 8L14 14L2 8Z" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <path d="M10 8H2" stroke="currentColor" stroke-width="1.2"/>
    </svg>`,

    attach: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8.5 3.5L4 8C3.5 8.5 3.5 9.5 4 10S5.5 10.5 6 10L10.5 5.5C11.5 4.5 11.5 2.5 10.5 1.5S8.5 1.5 7.5 2.5L3 7C1.5 8.5 1.5 11.5 3 13S6.5 13.5 8 12L12.5 7.5" stroke="currentColor" stroke-width="1.2" fill="none" stroke-linecap="round"/>
    </svg>`,

    clear: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M3 6H13L12 13H4L3 6Z" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <path d="M6 3H10V6" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <path d="M1 6H15" stroke="currentColor" stroke-width="1.2"/>
    </svg>`,

    gitBranch: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="3" cy="3" r="2" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <circle cx="13" cy="13" r="2" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <circle cx="13" cy="3" r="2" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <path d="M5 3H11" stroke="currentColor" stroke-width="1.2"/>
        <path d="M13 5V11" stroke="currentColor" stroke-width="1.2"/>
    </svg>`,

    chevronDown: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 6L8 10L12 6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    chevronUp: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 10L8 6L4 10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    chevronRight: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    eye: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 8S3 3 8 3S15 8 15 8S13 13 8 13S1 8 1 8Z" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <circle cx="8" cy="8" r="2" stroke="currentColor" stroke-width="1.2" fill="none"/>
    </svg>`,

    warning: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 1L15 14H1L8 1Z" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <path d="M8 6V9" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
        <circle cx="8" cy="12" r="0.5" fill="currentColor"/>
    </svg>`,

    project: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="2" y="3" width="12" height="10" rx="1" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <path d="M2 6H14" stroke="currentColor" stroke-width="1.2"/>
        <circle cx="4" cy="4.5" r="0.5" fill="currentColor"/>
        <circle cx="5.5" cy="4.5" r="0.5" fill="currentColor"/>
        <circle cx="7" cy="4.5" r="0.5" fill="currentColor"/>
    </svg>`,

    play: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M5 3L13 8L5 13V3Z" stroke="currentColor" stroke-width="1.2" fill="currentColor"/>
    </svg>`,

    build: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 2L14 6V14H2V6L8 2Z" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <path d="M8 2V8" stroke="currentColor" stroke-width="1.2"/>
        <path d="M2 6L8 8L14 6" stroke="currentColor" stroke-width="1.2"/>
    </svg>`,

    test: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M6 2H10V4H12V6H10V8H12V10H10V12H6V10H4V8H6V6H4V4H6V2Z" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <circle cx="8" cy="7" r="1" fill="currentColor"/>
    </svg>`,

    deploy: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 1L15 8L8 15L1 8L8 1Z" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <path d="M8 5V11" stroke="currentColor" stroke-width="1.2" stroke-linecap="round"/>
        <path d="M6 9L8 11L10 9" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    shield: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 1L13 3V8C13 11 8 15 8 15S3 11 3 8V3L8 1Z" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <path d="M6 8L7.5 9.5L10 6.5" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    download: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 1V11" stroke="currentColor" stroke-width="1.2" stroke-linecap="round"/>
        <path d="M5 8L8 11L11 8" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M2 13H14" stroke="currentColor" stroke-width="1.2" stroke-linecap="round"/>
    </svg>`,

    javascript: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="1" y="1" width="14" height="14" rx="2" fill="#F7DF1E"/>
        <path d="M4.5 11.5C4.5 12.3 5.2 12.5 5.8 12.5C6.4 12.5 6.8 12.2 6.8 11.5V7H5.5V11.5Z" fill="#000"/>
        <path d="M8.5 12.5C9.5 12.5 10.2 12 10.2 11C10.2 10.1 9.7 9.8 8.8 9.5L8.5 9.4C8.1 9.2 7.9 9.1 7.9 8.8C7.9 8.6 8.1 8.4 8.4 8.4C8.7 8.4 8.9 8.5 9 8.8L10.1 8.2C9.8 7.6 9.2 7.3 8.4 7.3C7.5 7.3 6.8 7.8 6.8 8.7C6.8 9.5 7.3 9.9 8.1 10.2L8.4 10.3C8.8 10.5 9.1 10.6 9.1 11C9.1 11.3 8.8 11.5 8.4 11.5C7.9 11.5 7.6 11.2 7.5 10.8L6.4 11.4C6.7 12.1 7.4 12.5 8.5 12.5Z" fill="#000"/>
    </svg>`,

    react: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="8" cy="8" r="1.5" fill="#61DAFB"/>
        <ellipse cx="8" cy="8" rx="6" ry="2.5" stroke="#61DAFB" stroke-width="1" fill="none"/>
        <ellipse cx="8" cy="8" rx="6" ry="2.5" stroke="#61DAFB" stroke-width="1" fill="none" transform="rotate(60 8 8)"/>
        <ellipse cx="8" cy="8" rx="6" ry="2.5" stroke="#61DAFB" stroke-width="1" fill="none" transform="rotate(-60 8 8)"/>
    </svg>`,

    nodejs: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 1L14.5 4.5V11.5L8 15L1.5 11.5V4.5L8 1Z" stroke="#339933" stroke-width="1.2" fill="#339933" opacity="0.8"/>
        <path d="M8 1V8M8 8L14.5 4.5M8 8L1.5 4.5" stroke="#fff" stroke-width="0.8"/>
    </svg>`,

    rust: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="8" cy="8" r="6" stroke="#CE422B" stroke-width="1.2" fill="none"/>
        <circle cx="8" cy="8" r="3" stroke="#CE422B" stroke-width="1" fill="#CE422B" opacity="0.3"/>
        <circle cx="8" cy="4" r="0.8" fill="#CE422B"/>
        <circle cx="12" cy="8" r="0.8" fill="#CE422B"/>
        <circle cx="8" cy="12" r="0.8" fill="#CE422B"/>
        <circle cx="4" cy="8" r="0.8" fill="#CE422B"/>
    </svg>`,

    python: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M5 2H11C12.1 2 13 2.9 13 4V6H8V7H13V12C13 13.1 12.1 14 11 14H5C3.9 14 3 13.1 3 12V4C3 2.9 3.9 2 5 2Z" fill="#3776AB"/>
        <path d="M8 7H3V4C3 2.9 3.9 2 5 2H8V7Z" fill="#FFD43B"/>
        <circle cx="5.5" cy="4.5" r="0.5" fill="#fff"/>
        <circle cx="10.5" cy="11.5" r="0.5" fill="#fff"/>
    </svg>`,

    debug: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="8" cy="8" r="6" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <path d="M6 6L10 10M10 6L6 10" stroke="currentColor" stroke-width="1.2" stroke-linecap="round"/>
        <circle cx="8" cy="8" r="1" fill="currentColor"/>
    </svg>`,

    stop: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="4" y="4" width="8" height="8" rx="1" stroke="currentColor" stroke-width="1.2" fill="currentColor"/>
    </svg>`,

    stepOver: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2 8H14" stroke="currentColor" stroke-width="1.2" stroke-linecap="round"/>
        <path d="M10 4L14 8L10 12" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
        <circle cx="6" cy="8" r="1" fill="currentColor"/>
    </svg>`,

    stepInto: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 2V14" stroke="currentColor" stroke-width="1.2" stroke-linecap="round"/>
        <path d="M4 10L8 14L12 10" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
        <circle cx="8" cy="6" r="1" fill="currentColor"/>
    </svg>`,

    stepOut: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 14V2" stroke="currentColor" stroke-width="1.2" stroke-linecap="round"/>
        <path d="M12 6L8 2L4 6" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
        <circle cx="8" cy="10" r="1" fill="currentColor"/>
    </svg>`,

    continue: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M3 3L13 8L3 13V3Z" stroke="currentColor" stroke-width="1.2" fill="currentColor"/>
        <path d="M13 3V13" stroke="currentColor" stroke-width="1.2" stroke-linecap="round"/>
    </svg>`,

    toggle: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="2" y="5" width="12" height="6" rx="3" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <circle cx="11" cy="8" r="2" fill="currentColor"/>
    </svg>`,

    toggleOff: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="2" y="5" width="12" height="6" rx="3" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <circle cx="5" cy="8" r="2" fill="currentColor"/>
    </svg>`,

    checkCircle: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="8" cy="8" r="6" stroke="#28a745" stroke-width="1.2" fill="#28a745" opacity="0.1"/>
        <path d="M5 8L7 10L11 6" stroke="#28a745" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    errorCircle: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="8" cy="8" r="6" stroke="#dc3545" stroke-width="1.2" fill="#dc3545" opacity="0.1"/>
        <path d="M6 6L10 10M10 6L6 10" stroke="#dc3545" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    skipCircle: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="8" cy="8" r="6" stroke="#ffc107" stroke-width="1.2" fill="#ffc107" opacity="0.1"/>
        <path d="M5 8H11" stroke="#ffc107" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    circle: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="8" cy="8" r="6" stroke="currentColor" stroke-width="1.2" fill="none"/>
    </svg>`,

    chart: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2 14V2H14" stroke="currentColor" stroke-width="1.2" stroke-linecap="round"/>
        <path d="M4 12L6 8L8 10L10 6L12 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
        <circle cx="6" cy="8" r="1" fill="currentColor"/>
        <circle cx="8" cy="10" r="1" fill="currentColor"/>
        <circle cx="10" cy="6" r="1" fill="currentColor"/>
        <circle cx="12" cy="8" r="1" fill="currentColor"/>
    </svg>`,

    replace: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M3 8H13M13 8L10 5M13 8L10 11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M13 4H3M3 4L6 1M3 4L6 7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    filter: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2 3H14L10 8V13L6 11V8L2 3Z" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"/>
    </svg>`,

    // Terminal Icons
    terminal: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="1" y="3" width="14" height="10" rx="1" stroke="currentColor" stroke-width="1.5"/>
        <path d="M4 7L6 9L4 11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M8 11H12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    split: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="1" y="2" width="6" height="12" rx="1" stroke="currentColor" stroke-width="1.5"/>
        <rect x="9" y="2" width="6" height="12" rx="1" stroke="currentColor" stroke-width="1.5"/>
    </svg>`,

    // Settings Icons
    settings: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 10C9.10457 10 10 9.10457 10 8C10 6.89543 9.10457 6 8 6C6.89543 6 6 6.89543 6 8C6 9.10457 6.89543 10 8 10Z" stroke="currentColor" stroke-width="1.2" fill="none"/>
        <path d="M6.7 1.5L7.3 1.5C7.6866 1.5 8 1.8134 8 2.2L8 2.8C8.3 2.9 8.6 3.1 8.8 3.3L9.3 2.8C9.5828 2.5172 10.0172 2.5172 10.3 2.8L10.7 3.2C10.9828 3.4828 10.9828 3.9172 10.7 4.2L10.2 4.7C10.4 4.9 10.6 5.2 10.7 5.5L11.3 5.5C11.6866 5.5 12 5.8134 12 6.2L12 6.8C12 7.1866 11.6866 7.5 11.3 7.5L10.7 7.5C10.6 7.8 10.4 8.1 10.2 8.3L10.7 8.8C10.9828 9.0828 10.9828 9.5172 10.7 9.8L10.3 10.2C10.0172 10.4828 9.5828 10.4828 9.3 10.2L8.8 9.7C8.6 9.9 8.3 10.1 8 10.2L8 10.8C8 11.1866 7.6866 11.5 7.3 11.5L6.7 11.5C6.3134 11.5 6 11.1866 6 10.8L6 10.2C5.7 10.1 5.4 9.9 5.2 9.7L4.7 10.2C4.4172 10.4828 3.9828 10.4828 3.7 10.2L3.3 9.8C3.0172 9.5172 3.0172 9.0828 3.3 8.8L3.8 8.3C3.6 8.1 3.4 7.8 3.3 7.5L2.7 7.5C2.3134 7.5 2 7.1866 2 6.8L2 6.2C2 5.8134 2.3134 5.5 2.7 5.5L3.3 5.5C3.4 5.2 3.6 4.9 3.8 4.7L3.3 4.2C3.0172 3.9172 3.0172 3.4828 3.3 3.2L3.7 2.8C3.9828 2.5172 4.4172 2.5172 4.7 2.8L5.2 3.3C5.4 3.1 5.7 2.9 6 2.8L6 2.2C6 1.8134 6.3134 1.5 6.7 1.5Z" stroke="currentColor" stroke-width="1.2" fill="none"/>
    </svg>`,

    palette: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 2C11.866 2 15 5.134 15 9C15 10.1046 14.1046 11 13 11H11C10.4477 11 10 11.4477 10 12C10 12.5523 10.4477 13 11 13C11.5523 13 12 13.4477 12 14C12 14.5523 11.5523 15 11 15H8C4.134 15 1 11.866 1 8C1 4.134 4.134 1 8 1V2Z" stroke="currentColor" stroke-width="1.5"/>
        <circle cx="5.5" cy="6.5" r="1" fill="currentColor"/>
        <circle cx="8.5" cy="5.5" r="1" fill="currentColor"/>
        <circle cx="11.5" cy="7.5" r="1" fill="currentColor"/>
    </svg>`,

    // File Type Icons
    javascript: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="1" y="1" width="14" height="14" rx="2" fill="#F7DF1E"/>
        <path d="M8.5 11.5C8.5 12.3284 7.82843 13 7 13C6.17157 13 5.5 12.3284 5.5 11.5V8.5H6.5V11.5C6.5 11.7761 6.72386 12 7 12C7.27614 12 7.5 11.7761 7.5 11.5V8.5H8.5V11.5Z" fill="#000"/>
        <path d="M10.5 13C9.67157 13 9 12.3284 9 11.5H10C10 11.7761 10.2239 12 10.5 12C10.7761 12 11 11.7761 11 11.5C11 11.2239 10.7761 11 10.5 11H10V10H10.5C11.3284 10 12 10.6716 12 11.5C12 12.3284 11.3284 13 10.5 13Z" fill="#000"/>
    </svg>`,

    rust: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="1" y="1" width="14" height="14" rx="2" fill="#CE422B"/>
        <path d="M8 3L9.5 6H13L10.5 8.5L11.5 12L8 10L4.5 12L5.5 8.5L3 6H6.5L8 3Z" fill="white"/>
    </svg>`,

    python: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="1" y="1" width="14" height="14" rx="2" fill="#3776AB"/>
        <circle cx="6" cy="6" r="1.5" fill="#FFD43B"/>
        <circle cx="10" cy="10" r="1.5" fill="#FFD43B"/>
        <path d="M4 8C4 6 6 4 8 4C10 4 12 6 12 8" stroke="#FFD43B" stroke-width="1"/>
        <path d="M12 8C12 10 10 12 8 12C6 12 4 10 4 8" stroke="#FFD43B" stroke-width="1"/>
    </svg>`,

    html: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="1" y="1" width="14" height="14" rx="2" fill="#E34F26"/>
        <path d="M3 3L4 12L8 13L12 12L13 3H3Z" fill="white"/>
        <path d="M8 4V12L11 11.2L11.8 4H8Z" fill="#E34F26"/>
    </svg>`,

    css: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="1" y="1" width="14" height="14" rx="2" fill="#1572B6"/>
        <path d="M3 3L4 12L8 13L12 12L13 3H3Z" fill="white"/>
        <path d="M8 4V12L11 11.2L11.8 4H8Z" fill="#1572B6"/>
    </svg>`,

    json: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="1" y="1" width="14" height="14" rx="2" fill="#000000"/>
        <path d="M4 6C4 5.44772 4.44772 5 5 5H6V6H5V10H6V11H5C4.44772 11 4 10.5523 4 10V6Z" fill="#FFD700"/>
        <path d="M12 6C12 5.44772 11.5523 5 11 5H10V6H11V10H10V11H11C11.5523 11 12 10.5523 12 10V6Z" fill="#FFD700"/>
        <circle cx="8" cy="6" r="0.5" fill="#FFD700"/>
        <circle cx="8" cy="8" r="0.5" fill="#FFD700"/>
        <circle cx="8" cy="10" r="0.5" fill="#FFD700"/>
    </svg>`,

    markdown: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="1" y="1" width="14" height="14" rx="2" fill="#083FA1"/>
        <path d="M3 5V11H4.5V7.5L6 9L7.5 7.5V11H9V5H7.5L6 6.5L4.5 5H3Z" fill="white"/>
        <path d="M10 8.5V11H11.5V8.5L13 10V8.5L11.75 7.25L10 8.5Z" fill="white"/>
    </svg>`,

    // Status Icons
    check: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M3 8L6 11L13 4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    warning: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 2L14 13H2L8 2Z" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"/>
        <path d="M8 6V9" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
        <circle cx="8" cy="11" r="0.5" fill="currentColor"/>
    </svg>`,

    error: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="8" cy="8" r="6" stroke="currentColor" stroke-width="1.5"/>
        <path d="M6 6L10 10M10 6L6 10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    info: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="8" cy="8" r="6" stroke="currentColor" stroke-width="1.5"/>
        <path d="M8 7V11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
        <circle cx="8" cy="5" r="0.5" fill="currentColor"/>
    </svg>`,

    // Menu Icons
    menu: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2 4H14M2 8H14M2 12H14" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    save: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2 2V14H14V4L12 2H2Z" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"/>
        <path d="M4 2V6H10V2" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"/>
        <rect x="5" y="9" width="6" height="4" stroke="currentColor" stroke-width="1.5"/>
    </svg>`,

    open: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 3C1 2.44772 1.44772 2 2 2H6L7.5 3.5H14C14.5523 3.5 15 3.94772 15 4.5V12C15 12.5523 14.5523 13 14 13H2C1.44772 13 1 12.5523 1 12V3Z" stroke="currentColor" stroke-width="1.5"/>
        <path d="M6 7L8 9L12 5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    // Edit Icons
    edit: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M11.5 2.5L13.5 4.5L5 13H3V11L11.5 2.5Z" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"/>
        <path d="M10.5 3.5L12.5 5.5" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"/>
    </svg>`,

    trash: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M3 4H13M5 4V3C5 2.44772 5.44772 2 6 2H10C10.5523 2 11 2.44772 11 3V4M6 7V11M10 7V11M4 4L4.5 12C4.5 12.5523 4.94772 13 5.5 13H10.5C11.0523 13 11.5 12.5523 11.5 12L12 4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    cut: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="4" cy="4" r="2" stroke="currentColor" stroke-width="1.5"/>
        <circle cx="4" cy="12" r="2" stroke="currentColor" stroke-width="1.5"/>
        <path d="M6 6L14 2M6 10L14 14M8 8L10 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    copy: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="4" y="4" width="8" height="8" rx="1" stroke="currentColor" stroke-width="1.5"/>
        <path d="M2 8V3C2 2.44772 2.44772 2 3 2H8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    paste: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="4" y="4" width="8" height="10" rx="1" stroke="currentColor" stroke-width="1.5"/>
        <path d="M6 2H10C10.5523 2 11 2.44772 11 3V4H5V3C5 2.44772 5.44772 2 6 2Z" stroke="currentColor" stroke-width="1.5"/>
        <path d="M7 8H9M7 10H9" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    // Git Icons
    gitBranch: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="4" cy="4" r="2" stroke="currentColor" stroke-width="1.5"/>
        <circle cx="12" cy="12" r="2" stroke="currentColor" stroke-width="1.5"/>
        <path d="M6 4H10C11.1046 4 12 4.89543 12 6V10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    // Loading Icons
    spinner: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 2V4M8 12V14M14 8H12M4 8H2M12.5 3.5L11.1 4.9M4.9 11.1L3.5 12.5M12.5 12.5L11.1 11.1M4.9 4.9L3.5 3.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" opacity="0.3"/>
        <path d="M8 2V4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    // Additional commonly used icons
    expand: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M6 4L10 8L6 12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    collapse: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 6L8 10L12 6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    // Error Console Icons
    bug: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 1C6.34 1 5 2.34 5 4V5H3V6H5V7C5 7.55 5.45 8 6 8H10C10.55 8 11 7.55 11 7V6H13V5H11V4C11 2.34 9.66 1 8 1ZM6 4C6 2.9 6.9 2 8 2C9.1 2 10 2.9 10 4V5H6V4ZM2 9V10H4V11C4 12.1 4.9 13 6 13H10C11.1 13 12 12.1 12 11V10H14V9H12V8H11V11C11 11.55 10.55 12 10 12H6C5.45 12 5 11.55 5 11V8H4V9H2ZM6 14V15H10V14H6Z" fill="currentColor"/>
    </svg>`,

    chevronUp: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 10L8 6L12 10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    chevronDown: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 6L8 10L12 6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    newFile: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M2 2C1.44772 2 1 2.44772 1 3V13C1 13.5523 1.44772 14 2 14H14C14.5523 14 15 13.5523 15 13V5.5L11.5 2H2Z" stroke="currentColor" stroke-width="1" fill="none"/>
        <path d="M11 2V5H14" stroke="currentColor" stroke-width="1" fill="none"/>
        <path d="M8 6V10M6 8H10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    newFolder: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 3C1 2.44772 1.44772 2 2 2H6L7.5 3.5H14C14.5523 3.5 15 3.94772 15 4.5V12C15 12.5523 14.5523 13 14 13H2C1.44772 13 1 12.5523 1 12V3Z" stroke="currentColor" stroke-width="1" fill="none"/>
        <path d="M8 7V11M6 9H10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    eye: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M1 8C1 8 3 3 8 3C13 3 15 8 15 8C15 8 13 13 8 13C3 13 1 8 1 8Z" stroke="currentColor" stroke-width="1.5"/>
        <circle cx="8" cy="8" r="2" stroke="currentColor" stroke-width="1.5"/>
    </svg>`,

    question: `<svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="8" cy="8" r="6" stroke="currentColor" stroke-width="1.5"/>
        <path d="M6 6C6 4.89543 6.89543 4 8 4C9.10457 4 10 4.89543 10 6C10 7 9 7.5 8 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
        <circle cx="8" cy="11" r="0.5" fill="currentColor"/>
    </svg>`,





    magic: `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M3 17L17 3" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
        <path d="M14 1L18 5L14 9L16 7L14 5L16 3L14 1Z" fill="currentColor"/>
        <circle cx="5" cy="5" r="1.5" fill="currentColor" opacity="0.4"/>
        <circle cx="15" cy="15" r="1.5" fill="currentColor" opacity="0.4"/>
        <path d="M4 4L2 2M4 4L6 2M4 4L2 6M4 4L6 6" stroke="currentColor" stroke-width="1" stroke-linecap="round"/>
        <path d="M16 16L14 14M16 16L18 14M16 16L14 18M16 16L18 18" stroke="currentColor" stroke-width="1" stroke-linecap="round"/>
    </svg>`,





    document: `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 2C3.44772 2 3 2.44772 3 3V17C3 17.5523 3.44772 18 4 18H16C16.5523 18 17 17.5523 17 17V7L12 2H4Z" stroke="currentColor" stroke-width="1.5" fill="currentColor" opacity="0.05"/>
        <path d="M12 2V7H17" stroke="currentColor" stroke-width="1.5" fill="none"/>
        <path d="M6 10H14M6 12H14M6 14H11" stroke="currentColor" stroke-width="1.2" stroke-linecap="round"/>
        <circle cx="15" cy="4" r="2" fill="currentColor" opacity="0.8"/>
        <path d="M14.2 4L15 4.8L16.8 3" stroke="white" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    sparkles: `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10 2L11 7L16 8L11 9L10 14L9 9L4 8L9 7L10 2Z" fill="currentColor" opacity="0.8"/>
        <path d="M16 4L16.5 5.5L18 6L16.5 6.5L16 8L15.5 6.5L14 6L15.5 5.5L16 4Z" fill="currentColor" opacity="0.6"/>
        <path d="M5 14L5.5 15.5L7 16L5.5 16.5L5 18L4.5 16.5L3 16L4.5 15.5L5 14Z" fill="currentColor" opacity="0.6"/>
    </svg>`,



    power: `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10 2V10" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        <path d="M15.5 6.5A7 7 0 1 1 4.5 6.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
        <circle cx="10" cy="10" r="2" fill="currentColor" opacity="0.3"/>
    </svg>`,

    activity: `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <polyline points="2,10 6,10 8,6 12,14 14,10 18,10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <circle cx="8" cy="6" r="1.5" fill="currentColor" opacity="0.6"/>
        <circle cx="12" cy="14" r="1.5" fill="currentColor" opacity="0.6"/>
    </svg>`,

    zap: `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <polygon points="11,1 4,12 9,12 8,19 15,8 10,8 11,1" fill="currentColor" opacity="0.8"/>
        <polygon points="11,1 4,12 9,12 8,19 15,8 10,8 11,1" stroke="currentColor" stroke-width="1" stroke-linejoin="round"/>
    </svg>`,

    shield: `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10 2L4 4V10C4 14 10 18 10 18S16 14 16 10V4L10 2Z" stroke="currentColor" stroke-width="1.5" fill="currentColor" opacity="0.1"/>
        <path d="M8 10L9 11L12 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    tool: `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M14.7 6.3A1 1 0 0 0 14.7 7.7L16.3 9.3A1 1 0 0 0 17.7 9.3L18.7 8.3A6 6 0 0 1 10.76 16.24L3.85 9.33A2.12 2.12 0 0 1 6.85 6.33L13.76 13.24A6 6 0 0 1 21.7 5.3L20.7 6.3A1 1 0 0 0 20.7 7.7L19.1 9.3A1 1 0 0 0 17.7 9.3L16.1 7.7A1 1 0 0 0 14.7 6.3Z" stroke="currentColor" stroke-width="1.5" fill="currentColor" opacity="0.1"/>
    </svg>`,

    barChart: `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="3" y="12" width="3" height="6" rx="1" fill="currentColor" opacity="0.7"/>
        <rect x="8" y="8" width="3" height="10" rx="1" fill="currentColor" opacity="0.7"/>
        <rect x="13" y="4" width="3" height="14" rx="1" fill="currentColor" opacity="0.7"/>
        <path d="M2 19H18" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
    </svg>`,

    checkCircle: `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="10" cy="10" r="8" stroke="currentColor" stroke-width="1.5" fill="currentColor" opacity="0.1"/>
        <path d="M6 10L8.5 12.5L14 7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,

    play: `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <polygon points="6,4 6,16 16,10" fill="currentColor" opacity="0.8"/>
        <polygon points="6,4 6,16 16,10" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"/>
    </svg>`,

    lightBulb: `<svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10 2A6 6 0 0 0 4 8V9A2 2 0 0 0 6 11H14A2 2 0 0 0 16 9V8A6 6 0 0 0 10 2Z" stroke="currentColor" stroke-width="1.5" fill="currentColor" opacity="0.1"/>
        <path d="M8 15H12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
        <path d="M9 17H11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
        <circle cx="10" cy="6" r="1" fill="currentColor" opacity="0.6"/>
    </svg>`
};

// Icon component helper
export function createIcon(iconName, size = 16, className = '') {
    const iconSvg = Icons[iconName];
    if (!iconSvg) {
        console.warn(`Icon "${iconName}" not found, using fallback`);
        // Return a fallback icon instead of empty string
        return `<span class="icon ${className}" style="width: ${size}px; height: ${size}px; display: inline-flex; align-items: center; justify-content: center;">
            <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 100%; height: 100%;">
                <rect x="2" y="2" width="12" height="12" rx="1" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <path d="M6 6L10 10M10 6L6 10" stroke="currentColor" stroke-width="1" stroke-linecap="round"/>
            </svg>
        </span>`;
    }

    // Ensure SVG has proper styling attributes
    const styledSvg = iconSvg.replace('<svg', '<svg style="width: 100%; height: 100%; display: block;"');
    return `<span class="icon ${className}" style="width: ${size}px; height: ${size}px; display: inline-flex; align-items: center; justify-content: center;">${styledSvg}</span>`;
}

// Get file type icon based on extension
export function getFileTypeIcon(filename) {
    const ext = filename.split('.').pop()?.toLowerCase();

    const iconMap = {
        'js': 'javascript',
        'jsx': 'javascript',
        'ts': 'javascript',
        'tsx': 'javascript',
        'rs': 'rust',
        'py': 'python',
        'html': 'html',
        'htm': 'html',
        'css': 'css',
        'scss': 'css',
        'sass': 'css',
        'less': 'css',
        'json': 'json',
        'md': 'markdown',
        'markdown': 'markdown'
    };

    return iconMap[ext] || 'file';
}

// Icon validation and debugging utilities
export function validateIcons() {
    const missingIcons = [];
    const validIcons = [];

    for (const [iconName, iconSvg] of Object.entries(Icons)) {
        if (!iconSvg || iconSvg.trim() === '') {
            missingIcons.push(iconName);
        } else if (!iconSvg.includes('<svg')) {
            missingIcons.push(`${iconName} (invalid SVG)`);
        } else {
            validIcons.push(iconName);
        }
    }

    console.log(`Icon validation complete:`);
    console.log(`✅ Valid icons: ${validIcons.length}`);
    console.log(`❌ Missing/invalid icons: ${missingIcons.length}`);

    if (missingIcons.length > 0) {
        console.warn('Missing or invalid icons:', missingIcons);
    }

    return {
        valid: validIcons,
        missing: missingIcons,
        total: Object.keys(Icons).length
    };
}

// Test icon rendering
export function testIconRendering(iconName, size = 16) {
    const testContainer = document.createElement('div');
    testContainer.style.cssText = 'position: fixed; top: 10px; right: 10px; background: white; padding: 10px; border: 1px solid #ccc; z-index: 9999;';

    const iconHtml = createIcon(iconName, size, 'test-icon');
    testContainer.innerHTML = `
        <div>Testing icon: ${iconName}</div>
        <div style="margin: 10px 0;">${iconHtml}</div>
        <button onclick="this.parentElement.remove()">Close</button>
    `;

    document.body.appendChild(testContainer);

    setTimeout(() => {
        if (testContainer.parentElement) {
            testContainer.remove();
        }
    }, 5000);
}

// Get all available icon names
export function getAvailableIcons() {
    return Object.keys(Icons).sort();
}

// Debug function to show all icons
export function showAllIcons() {
    const debugContainer = document.createElement('div');
    debugContainer.style.cssText = `
        position: fixed;
        top: 50px;
        left: 50px;
        right: 50px;
        bottom: 50px;
        background: white;
        padding: 20px;
        border: 2px solid #333;
        z-index: 10000;
        overflow: auto;
        font-family: monospace;
    `;

    const iconNames = getAvailableIcons();
    let html = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h2>All Available Icons (${iconNames.length})</h2>
            <button onclick="this.closest('div').parentElement.remove()" style="padding: 5px 10px;">Close</button>
        </div>
        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px;">
    `;

    iconNames.forEach(iconName => {
        const iconHtml = createIcon(iconName, 24, 'debug-icon');
        html += `
            <div style="display: flex; align-items: center; padding: 8px; border: 1px solid #eee; border-radius: 4px;">
                <div style="margin-right: 10px; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center;">
                    ${iconHtml}
                </div>
                <span style="font-size: 12px;">${iconName}</span>
            </div>
        `;
    });

    html += '</div>';
    debugContainer.innerHTML = html;
    document.body.appendChild(debugContainer);
}

// Make debug functions available globally for console testing
if (typeof window !== 'undefined') {
    window.RustCodeIcons = {
        validate: validateIcons,
        test: testIconRendering,
        showAll: showAllIcons,
        getAvailable: getAvailableIcons,
        create: createIcon
    };
}
