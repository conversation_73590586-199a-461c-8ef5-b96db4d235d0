/* Editor Container Styles */
#editor-container {
    flex: 1;
    position: relative;
    overflow: hidden;
}

#monaco-editor {
    width: 100%;
    height: 100%;
}

/* Monaco Editor Theme Overrides */
.monaco-editor {
    background-color: #1e1e1e !important;
}

.monaco-editor .margin {
    background-color: #1e1e1e !important;
}

.monaco-editor .monaco-editor-background {
    background-color: #1e1e1e !important;
}

.monaco-editor .current-line {
    background-color: #2a2a2a !important;
}

.monaco-editor .selected-text {
    background-color: #264f78 !important;
}

.monaco-editor .line-numbers {
    color: #858585 !important;
}

.monaco-editor .cursor {
    color: #ffffff !important;
}

/* Minimap */
.monaco-editor .minimap {
    background-color: #1e1e1e !important;
}

.monaco-editor .minimap-slider {
    background-color: rgba(100, 100, 100, 0.2) !important;
}

.monaco-editor .minimap-slider:hover {
    background-color: rgba(100, 100, 100, 0.3) !important;
}

/* Scrollbars */
.monaco-editor .monaco-scrollable-element > .scrollbar {
    background-color: transparent !important;
}

.monaco-editor .monaco-scrollable-element > .scrollbar > .slider {
    background-color: #3e3e42 !important;
}

.monaco-editor .monaco-scrollable-element > .scrollbar > .slider:hover {
    background-color: #4e4e52 !important;
}

/* Find widget */
.monaco-editor .find-widget {
    background-color: #252526 !important;
    border: 1px solid #3e3e42 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.monaco-editor .find-widget input {
    background-color: #3c3c3c !important;
    border: 1px solid #3e3e42 !important;
    color: #cccccc !important;
}

.monaco-editor .find-widget .button {
    background-color: transparent !important;
    color: #cccccc !important;
}

.monaco-editor .find-widget .button:hover {
    background-color: #3e3e42 !important;
}

/* Suggest widget */
.monaco-editor .suggest-widget {
    background-color: #252526 !important;
    border: 1px solid #3e3e42 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row {
    background-color: transparent !important;
    color: #cccccc !important;
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row:hover {
    background-color: #2a2d2e !important;
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row.focused {
    background-color: #094771 !important;
}

/* Context menu */
.monaco-editor .context-view {
    background-color: #383838 !important;
    border: 1px solid #5a5a5a !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.monaco-editor .monaco-menu .monaco-action-bar .action-item .action-label {
    color: #cccccc !important;
}

.monaco-editor .monaco-menu .monaco-action-bar .action-item .action-label:hover {
    background-color: #094771 !important;
}

/* Parameter hints */
.monaco-editor .parameter-hints-widget {
    background-color: #252526 !important;
    border: 1px solid #3e3e42 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.monaco-editor .parameter-hints-widget .signature {
    color: #cccccc !important;
}

.monaco-editor .parameter-hints-widget .parameter.active {
    color: #4ec9b0 !important;
}

/* Hover widget */
.monaco-editor .monaco-hover {
    background-color: #252526 !important;
    border: 1px solid #3e3e42 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.monaco-editor .monaco-hover .hover-contents {
    color: #cccccc !important;
}

/* Peek view */
.monaco-editor .zone-widget .zone-widget-container {
    background-color: #252526 !important;
    border: 1px solid #3e3e42 !important;
}

.monaco-editor .peekview-widget .head {
    background-color: #2d2d30 !important;
    border-bottom: 1px solid #3e3e42 !important;
}

.monaco-editor .peekview-widget .body {
    background-color: #1e1e1e !important;
}

/* Diff editor */
.monaco-diff-editor .diffOverview {
    background-color: #1e1e1e !important;
}

.monaco-diff-editor .diff-review-line-number {
    color: #858585 !important;
}

/* Breadcrumbs */
.monaco-editor .monaco-breadcrumbs {
    background-color: #2d2d30 !important;
    border-bottom: 1px solid #3e3e42 !important;
}

.monaco-editor .monaco-breadcrumbs .monaco-breadcrumb-item {
    color: #cccccc !important;
}

.monaco-editor .monaco-breadcrumbs .monaco-breadcrumb-item:hover {
    background-color: #3e3e42 !important;
}

/* Loading overlay */
.editor-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #1e1e1e;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.editor-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #3e3e42;
    border-top: 4px solid #007acc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error overlay */
.editor-error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #1e1e1e;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    z-index: 1000;
}

.editor-error-icon {
    font-size: 48px;
    color: #e74c3c;
    margin-bottom: 20px;
}

.editor-error-message {
    color: #cccccc;
    text-align: center;
    max-width: 400px;
    line-height: 1.5;
}

.editor-error-retry {
    margin-top: 20px;
    background-color: #007acc;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 13px;
}

.editor-error-retry:hover {
    background-color: #005a9e;
}

/* Enhanced Editor Loading Indicator */
.editor-loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-primary);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

.loading-text {
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
}

/* Enhanced Monaco Editor Features */
.monaco-editor .codelens-decoration {
    color: var(--text-secondary) !important;
    font-size: 12px !important;
    opacity: 0.8;
}

.monaco-editor .codelens-decoration:hover {
    color: var(--accent-color) !important;
    opacity: 1;
}

/* Inlay Hints */
.monaco-editor .monaco-editor-inlay-hint {
    background: var(--bg-tertiary) !important;
    color: var(--text-secondary) !important;
    border-radius: 3px !important;
    padding: 1px 4px !important;
    font-size: 11px !important;
    opacity: 0.8;
}

.monaco-editor .monaco-editor-inlay-hint:hover {
    opacity: 1;
}

/* Enhanced Performance Optimizations */
.monaco-editor .view-overlays .current-line {
    background: var(--bg-hover) !important;
}

.monaco-editor .margin-view-overlays .current-line-margin {
    background: var(--bg-hover) !important;
}

/* Enhanced Scrollbars */
.monaco-editor .monaco-scrollable-element > .scrollbar {
    background: transparent !important;
}

.monaco-editor .monaco-scrollable-element > .scrollbar.vertical {
    width: 12px !important;
}

.monaco-editor .monaco-scrollable-element > .scrollbar.horizontal {
    height: 12px !important;
}

/* Selection and Highlighting */
.monaco-editor .selected-text {
    background-color: var(--accent-color-alpha) !important;
}

.monaco-editor .focused .selected-text {
    background-color: var(--accent-color) !important;
    color: white !important;
}

/* Word Highlighting */
.monaco-editor .wordHighlight {
    background-color: rgba(87, 87, 87, 0.72) !important;
}

.monaco-editor .wordHighlightStrong {
    background-color: rgba(0, 73, 114, 0.72) !important;
}

/* Bracket Matching */
.monaco-editor .bracket-match {
    background-color: rgba(0, 100, 0, 0.3) !important;
    border: 1px solid rgba(0, 255, 0, 0.8) !important;
}

/* Enhanced Find Widget */
.monaco-editor .find-widget {
    background-color: var(--bg-primary) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

.monaco-editor .find-widget input {
    background-color: var(--bg-secondary) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
}

.monaco-editor .find-widget input:focus {
    border-color: var(--accent-color) !important;
    box-shadow: 0 0 0 2px var(--accent-color-alpha) !important;
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .editor-loading-indicator {
        padding: 20px;
    }

    .loading-spinner {
        width: 32px;
        height: 32px;
        border-width: 2px;
    }

    .loading-text {
        font-size: 12px;
    }

    .monaco-editor .suggest-widget {
        max-width: 90vw !important;
    }

    .monaco-editor .find-widget {
        max-width: 90vw !important;
    }

    .monaco-editor .monaco-hover {
        max-width: 90vw !important;
    }

    .monaco-editor .parameter-hints-widget {
        max-width: 90vw !important;
    }
}

/* Dark Theme Enhancements */
@media (prefers-color-scheme: dark) {
    .monaco-editor {
        background-color: var(--bg-primary) !important;
    }

    .monaco-editor .margin {
        background-color: var(--bg-primary) !important;
    }

    .monaco-editor .monaco-editor-background {
        background-color: var(--bg-primary) !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .monaco-editor .suggest-widget,
    .monaco-editor .find-widget,
    .monaco-editor .monaco-hover,
    .monaco-editor .parameter-hints-widget {
        border-width: 2px !important;
        box-shadow: 0 0 0 1px var(--accent-color) !important;
    }

    .monaco-editor .codelens-decoration {
        font-weight: bold !important;
    }

    .monaco-editor .monaco-editor-inlay-hint {
        border: 1px solid var(--border-color) !important;
    }
}
