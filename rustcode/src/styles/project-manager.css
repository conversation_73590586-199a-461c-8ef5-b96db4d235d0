/* Project Manager Styles */
.project-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--bg-secondary);
    border-left: 1px solid var(--border-color);
    font-family: var(--font-family);
    overflow: hidden;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    min-height: 48px;
}

.project-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.project-controls {
    display: flex;
    gap: 4px;
}

.project-btn {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.project-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.project-info {
    padding: 12px 16px;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
}

.current-project {
    margin-bottom: 12px;
}

.project-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
    display: block;
    margin-bottom: 4px;
}

.project-path {
    color: var(--text-secondary);
    font-size: 12px;
}

.environment-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.environment-selector label {
    color: var(--text-secondary);
    font-size: 12px;
}

#environment-select {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
}

.project-tabs {
    display: flex;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    overflow-x: auto;
}

.project-tab {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 12px 16px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
    white-space: nowrap;
    flex-shrink: 0;
}

.project-tab:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.project-tab.active {
    color: var(--accent-color);
    border-bottom-color: var(--accent-color);
}

.project-tab-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.tab-panel {
    display: none;
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.tab-panel.active {
    display: flex;
    flex-direction: column;
}

/* Overview Tab */
.project-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 16px;
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--accent-color);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary);
}

.project-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
    margin-bottom: 24px;
}

.action-btn {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 10px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.action-btn:hover {
    background: var(--bg-hover);
    border-color: var(--accent-color);
}

.action-btn.primary {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.action-btn.primary:hover {
    background: var(--accent-color-hover);
}

.recent-projects h3 {
    color: var(--text-primary);
    font-size: 14px;
    margin-bottom: 12px;
}

.recent-project-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.recent-project-item:hover {
    background: var(--bg-hover);
}

.recent-project-item .project-info {
    flex: 1;
    min-width: 0;
    padding: 0;
    background: none;
    border: none;
}

.recent-project-item .project-name {
    font-size: 13px;
    margin-bottom: 2px;
}

.recent-project-item .project-path {
    font-size: 11px;
}

.project-time {
    color: var(--text-secondary);
    font-size: 10px;
    margin-top: 2px;
}

.project-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.recent-project-item:hover .project-actions {
    opacity: 1;
}

.project-action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.project-action-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Dependencies Tab */
.dependencies-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.dependencies-header h3 {
    color: var(--text-primary);
    font-size: 14px;
    margin: 0;
}

.dependency-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.dependency-category {
    margin-bottom: 24px;
}

.dependency-category h4 {
    color: var(--text-primary);
    font-size: 13px;
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom: 1px solid var(--border-color);
}

.dependency-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 6px;
    transition: all 0.2s ease;
}

.dependency-item:hover {
    background: var(--bg-hover);
}

.dependency-info {
    flex: 1;
    min-width: 0;
}

.dependency-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 13px;
}

.dependency-version {
    color: var(--text-secondary);
    font-size: 11px;
    font-family: var(--font-mono);
}

.dependency-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.dependency-item:hover .dependency-actions {
    opacity: 1;
}

.dependency-action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.dependency-action-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Build Tab */
.build-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.build-header h3 {
    color: var(--text-primary);
    font-size: 14px;
    margin: 0;
}

.build-actions {
    display: flex;
    gap: 8px;
}

.build-task-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.build-task-item:hover {
    background: var(--bg-hover);
}

.task-info {
    flex: 1;
    min-width: 0;
}

.task-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 13px;
    margin-bottom: 4px;
}

.task-command {
    color: var(--text-secondary);
    font-size: 11px;
    font-family: var(--font-mono);
    background: var(--bg-tertiary);
    padding: 2px 6px;
    border-radius: 3px;
    margin-bottom: 4px;
    display: inline-block;
}

.task-description {
    color: var(--text-secondary);
    font-size: 11px;
}

.task-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.build-task-item:hover .task-actions {
    opacity: 1;
}

.task-action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.task-action-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.build-output {
    margin-top: 24px;
}

.build-output h4 {
    color: var(--text-primary);
    font-size: 13px;
    margin-bottom: 8px;
}

.output-console {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 12px;
    height: 200px;
    overflow-y: auto;
    font-family: var(--font-mono);
    font-size: 12px;
}

.console-line {
    color: var(--text-primary);
    margin-bottom: 2px;
    white-space: pre-wrap;
}

/* Templates Tab */
.templates-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.templates-header h3 {
    color: var(--text-primary);
    font-size: 14px;
    margin: 0;
}

.template-actions {
    display: flex;
    gap: 8px;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;
}

.template-item {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 16px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.template-item:hover {
    background: var(--bg-hover);
    border-color: var(--accent-color);
}

.template-icon {
    text-align: center;
    margin-bottom: 12px;
    color: var(--accent-color);
}

.template-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
    margin-bottom: 6px;
}

.template-description {
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 12px;
}

.template-action-btn {
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    font-weight: 500;
    transition: all 0.2s ease;
    width: 100%;
}

.template-action-btn:hover {
    background: var(--accent-color-hover);
}

/* Modals */
.project-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.project-modal.hidden {
    display: none;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: 8px;
    padding: 24px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
}

.modal-content h3 {
    margin: 0 0 20px 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 13px;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 13px;
    font-family: var(--font-family);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px var(--accent-color-alpha);
}

.form-group textarea {
    resize: vertical;
    min-height: 60px;
}

.location-input {
    display: flex;
    gap: 8px;
}

.location-input input {
    flex: 1;
}

.browse-btn {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    white-space: nowrap;
}

.browse-btn:hover {
    background: var(--bg-hover);
}

.form-group input[type="checkbox"] {
    margin-right: 8px;
}

.modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.btn-primary {
    background: var(--accent-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: var(--accent-color-hover);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background: var(--bg-hover);
}

.empty-state {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 32px 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .project-stats {
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    }
    
    .project-actions {
        grid-template-columns: 1fr;
    }
    
    .dependency-actions,
    .build-actions,
    .template-actions {
        flex-direction: column;
    }
    
    .template-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        margin: 20px;
        padding: 20px;
    }
    
    .modal-actions {
        flex-direction: column;
    }
}
