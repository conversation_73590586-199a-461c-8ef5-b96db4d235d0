/* Debug Test Manager Styles */
.debug-test-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--bg-secondary);
    border-left: 1px solid var(--border-color);
    font-family: var(--font-family);
    overflow: hidden;
}

.debug-test-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    min-height: 48px;
}

.debug-test-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.debug-test-controls {
    display: flex;
    gap: 4px;
}

.debug-test-btn {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.debug-test-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.debug-test-tabs {
    display: flex;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    overflow-x: auto;
}

.debug-test-tab {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 12px 16px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
    white-space: nowrap;
    flex-shrink: 0;
}

.debug-test-tab:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.debug-test-tab.active {
    color: var(--accent-color);
    border-bottom-color: var(--accent-color);
}

.debug-test-tab-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.tab-panel {
    display: none;
    flex: 1;
    overflow-y: auto;
    padding: 16px;
}

.tab-panel.active {
    display: flex;
    flex-direction: column;
}

/* Debug Controls */
.debug-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.debug-control-btn {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
    min-width: 0;
    justify-content: center;
}

.debug-control-btn:hover:not(:disabled) {
    background: var(--bg-hover);
    border-color: var(--accent-color);
}

.debug-control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.debug-control-btn.primary {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.debug-control-btn.primary:hover:not(:disabled) {
    background: var(--accent-color-hover);
}

/* Debug Sections */
.debug-sections {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.debug-section {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.section-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 600;
}

.section-actions {
    display: flex;
    gap: 4px;
}

.section-action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.section-action-btn:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

/* Breakpoints */
.breakpoints-list,
.call-stack-list,
.variables-list,
.watch-list {
    max-height: 200px;
    overflow-y: auto;
}

.breakpoint-item,
.call-stack-item,
.variable-item,
.watch-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.breakpoint-item:hover,
.call-stack-item:hover,
.variable-item:hover,
.watch-item:hover {
    background: var(--bg-hover);
}

.breakpoint-item.disabled {
    opacity: 0.5;
}

.call-stack-item.current {
    background: var(--accent-color-alpha);
    border-left: 3px solid var(--accent-color);
}

.breakpoint-info,
.frame-info,
.variable-info,
.watch-info {
    flex: 1;
    min-width: 0;
}

.breakpoint-location,
.frame-function,
.variable-name,
.watch-expression {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 13px;
    margin-bottom: 2px;
}

.frame-location,
.variable-type,
.variable-value,
.watch-value {
    color: var(--text-secondary);
    font-size: 11px;
    font-family: var(--font-mono);
}

.breakpoint-actions,
.watch-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.breakpoint-item:hover .breakpoint-actions,
.watch-item:hover .watch-actions {
    opacity: 1;
}

.breakpoint-action-btn,
.watch-action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.breakpoint-action-btn:hover,
.watch-action-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Test Controls */
.test-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.test-control-btn {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
    min-width: 0;
    justify-content: center;
}

.test-control-btn:hover:not(:disabled) {
    background: var(--bg-hover);
    border-color: var(--accent-color);
}

.test-control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.test-control-btn.primary {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.test-control-btn.primary:hover:not(:disabled) {
    background: var(--accent-color-hover);
}

.test-filter {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

#test-filter-input {
    flex: 1;
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
}

#test-filter-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px var(--accent-color-alpha);
}

#test-status-filter {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
}

.test-summary {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
    padding: 16px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
}

.summary-stat {
    text-align: center;
    flex: 1;
}

.summary-stat .stat-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.summary-stat .stat-label {
    font-size: 12px;
    color: var(--text-secondary);
}

.summary-stat.passed .stat-value {
    color: var(--success-color);
}

.summary-stat.failed .stat-value {
    color: var(--error-color);
}

.summary-stat.skipped .stat-value {
    color: var(--warning-color);
}

/* Test Suites */
.test-suite {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 12px;
    overflow: hidden;
}

.suite-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.suite-info {
    flex: 1;
    min-width: 0;
}

.suite-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
    margin-bottom: 2px;
}

.suite-file {
    color: var(--text-secondary);
    font-size: 11px;
}

.suite-stats {
    display: flex;
    gap: 8px;
}

.suite-stats .stat {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
    min-width: 20px;
    text-align: center;
}

.suite-stats .stat.passed {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.suite-stats .stat.failed {
    background: rgba(220, 53, 69, 0.1);
    color: var(--error-color);
}

.suite-stats .stat.skipped {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.test-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 16px;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.test-item:hover {
    background: var(--bg-hover);
}

.test-item:last-child {
    border-bottom: none;
}

.test-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
}

.test-details {
    flex: 1;
    min-width: 0;
}

.test-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 13px;
    margin-bottom: 2px;
}

.test-duration {
    color: var(--text-secondary);
    font-size: 11px;
}

.test-error {
    color: var(--error-color);
    font-size: 11px;
    margin-top: 2px;
}

.test-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.test-item:hover .test-actions {
    opacity: 1;
}

.test-action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.test-action-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Profiler */
.profiler-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    align-items: center;
    flex-wrap: wrap;
}

.profiler-control-btn {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.profiler-control-btn:hover:not(:disabled) {
    background: var(--bg-hover);
    border-color: var(--accent-color);
}

.profiler-control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.profiler-control-btn.primary {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.profiler-control-btn.primary:hover:not(:disabled) {
    background: var(--accent-color-hover);
}

#profiler-type {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 12px;
}

.profiler-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
}

.profiler-stats .stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 16px;
    text-align: center;
}

.profiler-stats .stat-value {
    font-size: 20px;
    font-weight: 600;
    color: var(--accent-color);
    margin-bottom: 4px;
}

.profiler-stats .stat-label {
    font-size: 12px;
    color: var(--text-secondary);
}

.profiler-chart {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-placeholder {
    text-align: center;
    color: var(--text-secondary);
}

.chart-icon {
    margin-bottom: 12px;
    opacity: 0.5;
}

.chart-text {
    font-size: 14px;
}

.chart-active {
    text-align: center;
    color: var(--accent-color);
}

.chart-line {
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-color), transparent);
    margin-bottom: 12px;
    animation: pulse 2s infinite;
}

.chart-data {
    font-size: 14px;
    font-weight: 500;
}

.profiler-hotspots h4 {
    color: var(--text-primary);
    font-size: 14px;
    margin-bottom: 12px;
}

.hotspot-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 6px;
}

.hotspot-info {
    flex: 1;
    min-width: 0;
}

.hotspot-function {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 13px;
    margin-bottom: 2px;
}

.hotspot-file {
    color: var(--text-secondary);
    font-size: 11px;
}

.hotspot-stats {
    display: flex;
    gap: 12px;
    align-items: center;
}

.hotspot-time {
    color: var(--text-primary);
    font-size: 12px;
    font-family: var(--font-mono);
}

.hotspot-percentage {
    color: var(--accent-color);
    font-size: 12px;
    font-weight: 500;
}

/* Coverage */
.coverage-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.coverage-control-btn {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.coverage-control-btn:hover {
    background: var(--bg-hover);
    border-color: var(--accent-color);
}

.coverage-control-btn.primary {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.coverage-control-btn.primary:hover {
    background: var(--accent-color-hover);
}

.coverage-summary {
    display: flex;
    gap: 20px;
    margin-bottom: 24px;
    justify-content: center;
}

.coverage-stat {
    text-align: center;
}

.coverage-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 4px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 8px;
    position: relative;
}

.coverage-circle.good {
    border-color: var(--success-color);
    background: rgba(40, 167, 69, 0.1);
}

.coverage-circle.medium {
    border-color: var(--warning-color);
    background: rgba(255, 193, 7, 0.1);
}

.coverage-circle.poor {
    border-color: var(--error-color);
    background: rgba(220, 53, 69, 0.1);
}

.coverage-percentage {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.coverage-label {
    font-size: 12px;
    color: var(--text-secondary);
}

.coverage-files h4 {
    color: var(--text-primary);
    font-size: 14px;
    margin-bottom: 12px;
}

.coverage-file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 6px;
}

.coverage-file-item .file-info {
    flex: 1;
    min-width: 0;
}

.coverage-file-item .file-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 13px;
    margin-bottom: 2px;
}

.coverage-file-item .file-lines {
    color: var(--text-secondary);
    font-size: 11px;
}

.file-coverage {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.file-coverage.good {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.file-coverage.medium {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.file-coverage.poor {
    background: rgba(220, 53, 69, 0.1);
    color: var(--error-color);
}

/* Modals */
.debug-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.debug-modal.hidden {
    display: none;
}

.debug-modal .modal-content {
    background: var(--bg-primary);
    border-radius: 8px;
    padding: 24px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid var(--border-color);
}

.empty-state {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 32px 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .debug-controls,
    .test-controls,
    .profiler-controls,
    .coverage-controls {
        flex-direction: column;
    }
    
    .debug-control-btn,
    .test-control-btn,
    .profiler-control-btn,
    .coverage-control-btn {
        flex: none;
    }
    
    .test-summary {
        flex-direction: column;
        gap: 8px;
    }
    
    .coverage-summary {
        flex-direction: column;
        gap: 16px;
    }
    
    .profiler-stats {
        grid-template-columns: 1fr;
    }
}
