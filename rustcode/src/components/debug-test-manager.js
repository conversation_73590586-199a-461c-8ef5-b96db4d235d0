import { createIcon } from '../icons/icon-library.js';

export class DebugTestManager {
    constructor(container, editorManager) {
        this.container = container;
        this.editorManager = editorManager;
        this.isVisible = false;
        this.debugSession = null;
        this.breakpoints = new Map();
        this.watchExpressions = [];
        this.testSuites = [];
        this.testResults = new Map();
        this.profilerData = null;
        this.coverageData = null;
        this.isDebugging = false;
        this.isProfiling = false;
        this.currentMode = 'debug'; // 'debug', 'test', 'profile'
        this.debugConfigurations = [];
        this.testFrameworks = ['jest', 'mocha', 'vitest', 'cargo-test', 'pytest', 'junit', 'go-test'];
        this.currentTestFramework = 'jest';
        this.testCoverage = {
            lines: 0,
            functions: 0,
            branches: 0,
            statements: 0,
            files: new Map()
        };
        this.debugHistory = [];
        this.testHistory = [];
        this.performanceMetrics = {
            testExecutionTime: 0,
            debugSessionTime: 0,
            lastTestRun: null,
            lastDebugSession: null,
            totalTestsRun: 0,
            totalDebugSessions: 0
        };
        this.settings = {
            autoRunTests: false,
            showTestCoverage: true,
            enableDebugConsole: true,
            enablePerformanceMonitoring: true,
            testTimeout: 30000,
            debugTimeout: 60000,
            enableAdvancedDebugging: true,
            enableTestReporting: true,
            enableContinuousIntegration: false,
            enableParallelTesting: true,
            maxParallelTests: 4
        };
        this.eventListeners = [];
        this.testWatchers = new Map();
        this.debugAdapters = new Map();
        this.activeProcesses = new Map();

        this.init();
    }

    init() {
        this.createUI();
        this.setupEventListeners();
        this.loadTestData();
        this.initializeDebugger();
    }

    createUI() {
        this.container.innerHTML = `
            <div class="debug-test-panel">
                <div class="debug-test-header">
                    <div class="debug-test-title">
                        ${createIcon('debug', 16)} Debug & Test
                    </div>
                    <div class="debug-test-controls">
                        <button class="debug-test-btn" id="debug-refresh-btn" title="Refresh">
                            ${createIcon('refresh', 14)}
                        </button>
                        <button class="debug-test-btn" id="debug-settings-btn" title="Settings">
                            ${createIcon('settings', 14)}
                        </button>
                        <button class="debug-test-btn" id="debug-close-btn" title="Close">
                            ${createIcon('close', 14)}
                        </button>
                    </div>
                </div>

                <div class="debug-test-tabs">
                    <button class="debug-test-tab active" data-tab="debugger">Debugger</button>
                    <button class="debug-test-tab" data-tab="testing">Testing</button>
                    <button class="debug-test-tab" data-tab="profiler">Profiler</button>
                    <button class="debug-test-tab" data-tab="coverage">Coverage</button>
                </div>

                <div class="debug-test-tab-content">
                    <!-- Debugger Tab -->
                    <div class="tab-panel active" id="debugger-panel">
                        <div class="debug-controls">
                            <button class="debug-control-btn primary" id="start-debug-btn">
                                ${createIcon('play', 14)} Start Debugging
                            </button>
                            <button class="debug-control-btn" id="stop-debug-btn" disabled>
                                ${createIcon('stop', 14)} Stop
                            </button>
                            <button class="debug-control-btn" id="step-over-btn" disabled>
                                ${createIcon('stepOver', 14)} Step Over
                            </button>
                            <button class="debug-control-btn" id="step-into-btn" disabled>
                                ${createIcon('stepInto', 14)} Step Into
                            </button>
                            <button class="debug-control-btn" id="step-out-btn" disabled>
                                ${createIcon('stepOut', 14)} Step Out
                            </button>
                            <button class="debug-control-btn" id="continue-btn" disabled>
                                ${createIcon('continue', 14)} Continue
                            </button>
                        </div>

                        <div class="debug-sections">
                            <!-- Breakpoints -->
                            <div class="debug-section">
                                <div class="section-header">
                                    <h4>Breakpoints</h4>
                                    <div class="section-actions">
                                        <button class="section-action-btn" id="clear-breakpoints-btn" title="Clear All">
                                            ${createIcon('clear', 12)}
                                        </button>
                                        <button class="section-action-btn" id="toggle-breakpoints-btn" title="Toggle All">
                                            ${createIcon('toggle', 12)}
                                        </button>
                                    </div>
                                </div>
                                <div class="breakpoints-list" id="breakpoints-list">
                                    <div class="empty-state">No breakpoints set</div>
                                </div>
                            </div>

                            <!-- Call Stack -->
                            <div class="debug-section">
                                <div class="section-header">
                                    <h4>Call Stack</h4>
                                </div>
                                <div class="call-stack-list" id="call-stack-list">
                                    <div class="empty-state">Not debugging</div>
                                </div>
                            </div>

                            <!-- Variables -->
                            <div class="debug-section">
                                <div class="section-header">
                                    <h4>Variables</h4>
                                    <div class="section-actions">
                                        <button class="section-action-btn" id="add-watch-btn" title="Add Watch">
                                            ${createIcon('plus', 12)}
                                        </button>
                                    </div>
                                </div>
                                <div class="variables-list" id="variables-list">
                                    <div class="empty-state">Not debugging</div>
                                </div>
                            </div>

                            <!-- Watch Expressions -->
                            <div class="debug-section">
                                <div class="section-header">
                                    <h4>Watch</h4>
                                </div>
                                <div class="watch-list" id="watch-list">
                                    <div class="empty-state">No watch expressions</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Testing Tab -->
                    <div class="tab-panel" id="testing-panel">
                        <div class="test-controls">
                            <button class="test-control-btn primary" id="run-all-tests-btn">
                                ${createIcon('play', 14)} Run All Tests
                            </button>
                            <button class="test-control-btn" id="run-failed-tests-btn">
                                ${createIcon('refresh', 14)} Run Failed
                            </button>
                            <button class="test-control-btn" id="debug-test-btn">
                                ${createIcon('debug', 14)} Debug Test
                            </button>
                            <button class="test-control-btn" id="stop-tests-btn" disabled>
                                ${createIcon('stop', 14)} Stop
                            </button>
                        </div>

                        <div class="test-filter">
                            <input type="text" id="test-filter-input" placeholder="Filter tests...">
                            <select id="test-status-filter">
                                <option value="all">All Tests</option>
                                <option value="passed">Passed</option>
                                <option value="failed">Failed</option>
                                <option value="skipped">Skipped</option>
                            </select>
                        </div>

                        <div class="test-summary" id="test-summary">
                            <div class="summary-stat">
                                <span class="stat-value" id="total-tests">0</span>
                                <span class="stat-label">Total</span>
                            </div>
                            <div class="summary-stat passed">
                                <span class="stat-value" id="passed-tests">0</span>
                                <span class="stat-label">Passed</span>
                            </div>
                            <div class="summary-stat failed">
                                <span class="stat-value" id="failed-tests">0</span>
                                <span class="stat-label">Failed</span>
                            </div>
                            <div class="summary-stat skipped">
                                <span class="stat-value" id="skipped-tests">0</span>
                                <span class="stat-label">Skipped</span>
                            </div>
                        </div>

                        <div class="test-suites" id="test-suites">
                            <!-- Test suites will be populated here -->
                        </div>
                    </div>

                    <!-- Profiler Tab -->
                    <div class="tab-panel" id="profiler-panel">
                        <div class="profiler-controls">
                            <button class="profiler-control-btn primary" id="start-profiler-btn">
                                ${createIcon('play', 14)} Start Profiling
                            </button>
                            <button class="profiler-control-btn" id="stop-profiler-btn" disabled>
                                ${createIcon('stop', 14)} Stop
                            </button>
                            <button class="profiler-control-btn" id="clear-profiler-btn">
                                ${createIcon('clear', 14)} Clear
                            </button>
                            <select id="profiler-type">
                                <option value="cpu">CPU Profiling</option>
                                <option value="memory">Memory Profiling</option>
                                <option value="performance">Performance</option>
                            </select>
                        </div>

                        <div class="profiler-stats" id="profiler-stats">
                            <div class="stat-card">
                                <div class="stat-value" id="cpu-usage">0%</div>
                                <div class="stat-label">CPU Usage</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="memory-usage">0 MB</div>
                                <div class="stat-label">Memory Usage</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value" id="execution-time">0 ms</div>
                                <div class="stat-label">Execution Time</div>
                            </div>
                        </div>

                        <div class="profiler-chart" id="profiler-chart">
                            <div class="chart-placeholder">
                                <div class="chart-icon">${createIcon('chart', 48)}</div>
                                <div class="chart-text">Start profiling to see performance data</div>
                            </div>
                        </div>

                        <div class="profiler-hotspots" id="profiler-hotspots">
                            <h4>Performance Hotspots</h4>
                            <div class="hotspots-list" id="hotspots-list">
                                <div class="empty-state">No profiling data available</div>
                            </div>
                        </div>
                    </div>

                    <!-- Coverage Tab -->
                    <div class="tab-panel" id="coverage-panel">
                        <div class="coverage-controls">
                            <button class="coverage-control-btn primary" id="run-coverage-btn">
                                ${createIcon('shield', 14)} Run Coverage
                            </button>
                            <button class="coverage-control-btn" id="clear-coverage-btn">
                                ${createIcon('clear', 14)} Clear
                            </button>
                            <button class="coverage-control-btn" id="export-coverage-btn">
                                ${createIcon('download', 14)} Export
                            </button>
                        </div>

                        <div class="coverage-summary" id="coverage-summary">
                            <div class="coverage-stat">
                                <div class="coverage-circle" id="line-coverage-circle">
                                    <span class="coverage-percentage">0%</span>
                                </div>
                                <div class="coverage-label">Line Coverage</div>
                            </div>
                            <div class="coverage-stat">
                                <div class="coverage-circle" id="branch-coverage-circle">
                                    <span class="coverage-percentage">0%</span>
                                </div>
                                <div class="coverage-label">Branch Coverage</div>
                            </div>
                            <div class="coverage-stat">
                                <div class="coverage-circle" id="function-coverage-circle">
                                    <span class="coverage-percentage">0%</span>
                                </div>
                                <div class="coverage-label">Function Coverage</div>
                            </div>
                        </div>

                        <div class="coverage-files" id="coverage-files">
                            <h4>File Coverage</h4>
                            <div class="coverage-files-list" id="coverage-files-list">
                                <div class="empty-state">No coverage data available</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Debug Configuration Modal -->
                <div class="debug-modal hidden" id="debug-config-modal">
                    <div class="modal-content">
                        <h3>Debug Configuration</h3>
                        <div class="form-group">
                            <label for="debug-type">Debug Type:</label>
                            <select id="debug-type">
                                <option value="node">Node.js</option>
                                <option value="browser">Browser</option>
                                <option value="python">Python</option>
                                <option value="rust">Rust</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="debug-program">Program:</label>
                            <input type="text" id="debug-program" placeholder="Path to program">
                        </div>
                        <div class="form-group">
                            <label for="debug-args">Arguments:</label>
                            <input type="text" id="debug-args" placeholder="Command line arguments">
                        </div>
                        <div class="form-group">
                            <label for="debug-cwd">Working Directory:</label>
                            <input type="text" id="debug-cwd" placeholder="Working directory">
                        </div>
                        <div class="form-group">
                            <label for="debug-env">Environment Variables:</label>
                            <textarea id="debug-env" placeholder="KEY=value (one per line)"></textarea>
                        </div>
                        <div class="modal-actions">
                            <button class="btn-primary" id="start-debug-config-btn">Start Debugging</button>
                            <button class="btn-secondary" id="cancel-debug-config-btn">Cancel</button>
                        </div>
                    </div>
                </div>

                <!-- Add Watch Modal -->
                <div class="debug-modal hidden" id="add-watch-modal">
                    <div class="modal-content">
                        <h3>Add Watch Expression</h3>
                        <div class="form-group">
                            <label for="watch-expression">Expression:</label>
                            <input type="text" id="watch-expression" placeholder="variable or expression">
                        </div>
                        <div class="modal-actions">
                            <button class="btn-primary" id="add-watch-confirm-btn">Add Watch</button>
                            <button class="btn-secondary" id="cancel-watch-btn">Cancel</button>
                        </div>
                    </div>
                </div>

                <!-- Test Configuration Modal -->
                <div class="debug-modal hidden" id="test-config-modal">
                    <div class="modal-content">
                        <h3>Test Configuration</h3>
                        <div class="form-group">
                            <label for="test-framework">Test Framework:</label>
                            <select id="test-framework">
                                <option value="jest">Jest</option>
                                <option value="mocha">Mocha</option>
                                <option value="pytest">PyTest</option>
                                <option value="cargo">Cargo Test</option>
                                <option value="go">Go Test</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="test-pattern">Test Pattern:</label>
                            <input type="text" id="test-pattern" placeholder="**/*.test.js">
                        </div>
                        <div class="form-group">
                            <label for="test-timeout">Timeout (ms):</label>
                            <input type="number" id="test-timeout" value="5000">
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="test-watch-mode"> Watch mode
                            </label>
                        </div>
                        <div class="modal-actions">
                            <button class="btn-primary" id="save-test-config-btn">Save Configuration</button>
                            <button class="btn-secondary" id="cancel-test-config-btn">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // Header controls
        this.container.querySelector('#debug-refresh-btn').addEventListener('click', () => {
            this.refreshDebugData();
        });

        this.container.querySelector('#debug-settings-btn').addEventListener('click', () => {
            this.showDebugSettings();
        });

        this.container.querySelector('#debug-close-btn').addEventListener('click', () => {
            this.hide();
        });

        // Tab switching
        this.container.querySelectorAll('.debug-test-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Debug controls
        this.container.querySelector('#start-debug-btn').addEventListener('click', () => {
            this.showDebugConfig();
        });

        this.container.querySelector('#stop-debug-btn').addEventListener('click', () => {
            this.stopDebugging();
        });

        this.container.querySelector('#step-over-btn').addEventListener('click', () => {
            this.stepOver();
        });

        this.container.querySelector('#step-into-btn').addEventListener('click', () => {
            this.stepInto();
        });

        this.container.querySelector('#step-out-btn').addEventListener('click', () => {
            this.stepOut();
        });

        this.container.querySelector('#continue-btn').addEventListener('click', () => {
            this.continueExecution();
        });

        // Breakpoint controls
        this.container.querySelector('#clear-breakpoints-btn').addEventListener('click', () => {
            this.clearAllBreakpoints();
        });

        this.container.querySelector('#toggle-breakpoints-btn').addEventListener('click', () => {
            this.toggleAllBreakpoints();
        });

        this.container.querySelector('#add-watch-btn').addEventListener('click', () => {
            this.showAddWatchModal();
        });

        // Test controls
        this.container.querySelector('#run-all-tests-btn').addEventListener('click', () => {
            this.runAllTests();
        });

        this.container.querySelector('#run-failed-tests-btn').addEventListener('click', () => {
            this.runFailedTests();
        });

        this.container.querySelector('#debug-test-btn').addEventListener('click', () => {
            this.debugTest();
        });

        this.container.querySelector('#stop-tests-btn').addEventListener('click', () => {
            this.stopTests();
        });

        // Test filter
        this.container.querySelector('#test-filter-input').addEventListener('input', (e) => {
            this.filterTests(e.target.value);
        });

        this.container.querySelector('#test-status-filter').addEventListener('change', (e) => {
            this.filterTestsByStatus(e.target.value);
        });

        // Profiler controls
        this.container.querySelector('#start-profiler-btn').addEventListener('click', () => {
            this.startProfiler();
        });

        this.container.querySelector('#stop-profiler-btn').addEventListener('click', () => {
            this.stopProfiler();
        });

        this.container.querySelector('#clear-profiler-btn').addEventListener('click', () => {
            this.clearProfiler();
        });

        // Coverage controls
        this.container.querySelector('#run-coverage-btn').addEventListener('click', () => {
            this.runCoverage();
        });

        this.container.querySelector('#clear-coverage-btn').addEventListener('click', () => {
            this.clearCoverage();
        });

        this.container.querySelector('#export-coverage-btn').addEventListener('click', () => {
            this.exportCoverage();
        });

        // Modal event listeners
        this.setupModalEventListeners();
    }

    setupModalEventListeners() {
        // Debug config modal
        this.container.querySelector('#start-debug-config-btn').addEventListener('click', () => {
            this.startDebuggingWithConfig();
        });

        this.container.querySelector('#cancel-debug-config-btn').addEventListener('click', () => {
            this.hideModal('debug-config-modal');
        });

        // Add watch modal
        this.container.querySelector('#add-watch-confirm-btn').addEventListener('click', () => {
            this.addWatchExpression();
        });

        this.container.querySelector('#cancel-watch-btn').addEventListener('click', () => {
            this.hideModal('add-watch-modal');
        });

        // Test config modal
        this.container.querySelector('#save-test-config-btn').addEventListener('click', () => {
            this.saveTestConfiguration();
        });

        this.container.querySelector('#cancel-test-config-btn').addEventListener('click', () => {
            this.hideModal('test-config-modal');
        });
    }

    loadTestData() {
        // Load test data from localStorage or initialize
        const savedTests = localStorage.getItem('rustcode-test-data');
        if (savedTests) {
            this.testSuites = JSON.parse(savedTests);
        } else {
            this.initializeSampleTests();
        }

        this.updateTestDisplay();
    }

    initializeSampleTests() {
        this.testSuites = [
            {
                name: 'Authentication Tests',
                file: 'tests/auth.test.js',
                tests: [
                    { name: 'should login with valid credentials', status: 'passed', duration: 45 },
                    { name: 'should reject invalid credentials', status: 'passed', duration: 32 },
                    { name: 'should handle missing password', status: 'failed', duration: 28, error: 'Expected error not thrown' }
                ]
            },
            {
                name: 'API Tests',
                file: 'tests/api.test.js',
                tests: [
                    { name: 'should fetch user data', status: 'passed', duration: 156 },
                    { name: 'should handle 404 errors', status: 'passed', duration: 89 },
                    { name: 'should validate request parameters', status: 'skipped', duration: 0 }
                ]
            },
            {
                name: 'Utils Tests',
                file: 'tests/utils.test.js',
                tests: [
                    { name: 'should format dates correctly', status: 'passed', duration: 12 },
                    { name: 'should validate email addresses', status: 'passed', duration: 8 },
                    { name: 'should handle edge cases', status: 'failed', duration: 15, error: 'Assertion failed' }
                ]
            }
        ];
    }

    initializeDebugger() {
        // Initialize debugger state
        this.debugSession = {
            isActive: false,
            currentLine: null,
            callStack: [],
            variables: new Map(),
            breakpoints: new Map()
        };
    }

    show() {
        this.isVisible = true;
        this.container.style.display = 'flex';
        this.refreshDebugData();
    }

    hide() {
        this.isVisible = false;
        this.container.style.display = 'none';
    }

    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    // Debug Methods
    showDebugConfig() {
        this.container.querySelector('#debug-config-modal').classList.remove('hidden');
    }

    startDebuggingWithConfig() {
        const type = this.container.querySelector('#debug-type').value;
        const program = this.container.querySelector('#debug-program').value;
        const args = this.container.querySelector('#debug-args').value;
        const cwd = this.container.querySelector('#debug-cwd').value;
        const env = this.container.querySelector('#debug-env').value;

        this.startDebugging({ type, program, args, cwd, env });
        this.hideModal('debug-config-modal');
    }

    startDebugging(config = {}) {
        this.isDebugging = true;
        this.debugSession.isActive = true;

        // Update UI
        this.updateDebugControls();
        this.simulateDebugSession();

        this.showNotification('Debug session started', 'success');
    }

    stopDebugging() {
        this.isDebugging = false;
        this.debugSession.isActive = false;
        this.debugSession.currentLine = null;
        this.debugSession.callStack = [];
        this.debugSession.variables.clear();

        this.updateDebugControls();
        this.updateCallStack();
        this.updateVariables();

        this.showNotification('Debug session stopped', 'info');
    }

    stepOver() {
        if (!this.isDebugging) return;
        this.simulateDebugStep('step-over');
        this.showNotification('Step over', 'info');
    }

    stepInto() {
        if (!this.isDebugging) return;
        this.simulateDebugStep('step-into');
        this.showNotification('Step into', 'info');
    }

    stepOut() {
        if (!this.isDebugging) return;
        this.simulateDebugStep('step-out');
        this.showNotification('Step out', 'info');
    }

    continueExecution() {
        if (!this.isDebugging) return;
        this.simulateDebugStep('continue');
        this.showNotification('Continue execution', 'info');
    }

    simulateDebugSession() {
        // Simulate a debug session with sample data
        this.debugSession.callStack = [
            { function: 'main', file: 'src/main.js', line: 42 },
            { function: 'processData', file: 'src/utils.js', line: 15 },
            { function: 'validateInput', file: 'src/validation.js', line: 8 }
        ];

        this.debugSession.variables.set('user', { type: 'object', value: '{ id: 1, name: "John" }' });
        this.debugSession.variables.set('count', { type: 'number', value: '42' });
        this.debugSession.variables.set('isValid', { type: 'boolean', value: 'true' });
        this.debugSession.variables.set('items', { type: 'array', value: '[1, 2, 3, 4, 5]' });

        this.updateCallStack();
        this.updateVariables();
    }

    simulateDebugStep(action) {
        // Simulate debug stepping
        const currentLine = this.debugSession.currentLine || 1;
        this.debugSession.currentLine = currentLine + 1;

        // Update variables randomly to simulate execution
        if (Math.random() > 0.7) {
            this.debugSession.variables.set('count', {
                type: 'number',
                value: (parseInt(this.debugSession.variables.get('count')?.value || '0') + 1).toString()
            });
        }

        this.updateCallStack();
        this.updateVariables();
    }

    updateDebugControls() {
        const startBtn = this.container.querySelector('#start-debug-btn');
        const stopBtn = this.container.querySelector('#stop-debug-btn');
        const stepBtns = this.container.querySelectorAll('#step-over-btn, #step-into-btn, #step-out-btn, #continue-btn');

        if (this.isDebugging) {
            startBtn.disabled = true;
            stopBtn.disabled = false;
            stepBtns.forEach(btn => btn.disabled = false);
        } else {
            startBtn.disabled = false;
            stopBtn.disabled = true;
            stepBtns.forEach(btn => btn.disabled = true);
        }
    }

    updateCallStack() {
        const container = this.container.querySelector('#call-stack-list');
        container.innerHTML = '';

        if (!this.isDebugging || this.debugSession.callStack.length === 0) {
            container.innerHTML = '<div class="empty-state">Not debugging</div>';
            return;
        }

        this.debugSession.callStack.forEach((frame, index) => {
            const item = document.createElement('div');
            item.className = 'call-stack-item';
            if (index === 0) item.classList.add('current');

            item.innerHTML = `
                <div class="frame-info">
                    <div class="frame-function">${frame.function}</div>
                    <div class="frame-location">${frame.file}:${frame.line}</div>
                </div>
            `;

            item.addEventListener('click', () => {
                this.navigateToFrame(frame);
            });

            container.appendChild(item);
        });
    }

    updateVariables() {
        const container = this.container.querySelector('#variables-list');
        container.innerHTML = '';

        if (!this.isDebugging || this.debugSession.variables.size === 0) {
            container.innerHTML = '<div class="empty-state">Not debugging</div>';
            return;
        }

        this.debugSession.variables.forEach((variable, name) => {
            const item = document.createElement('div');
            item.className = 'variable-item';

            item.innerHTML = `
                <div class="variable-info">
                    <div class="variable-name">${name}</div>
                    <div class="variable-type">${variable.type}</div>
                    <div class="variable-value">${variable.value}</div>
                </div>
            `;

            container.appendChild(item);
        });
    }

    navigateToFrame(frame) {
        // In a real implementation, this would navigate to the file and line
        this.showNotification(`Navigate to ${frame.file}:${frame.line}`, 'info');
    }

    // Breakpoint Management
    addBreakpoint(file, line) {
        const key = `${file}:${line}`;
        this.breakpoints.set(key, { file, line, enabled: true });
        this.updateBreakpointsList();
    }

    removeBreakpoint(file, line) {
        const key = `${file}:${line}`;
        this.breakpoints.delete(key);
        this.updateBreakpointsList();
    }

    toggleBreakpoint(file, line) {
        const key = `${file}:${line}`;
        if (this.breakpoints.has(key)) {
            this.removeBreakpoint(file, line);
        } else {
            this.addBreakpoint(file, line);
        }
    }

    clearAllBreakpoints() {
        this.breakpoints.clear();
        this.updateBreakpointsList();
        this.showNotification('All breakpoints cleared', 'info');
    }

    toggleAllBreakpoints() {
        this.breakpoints.forEach(bp => {
            bp.enabled = !bp.enabled;
        });
        this.updateBreakpointsList();
        this.showNotification('Breakpoints toggled', 'info');
    }

    updateBreakpointsList() {
        const container = this.container.querySelector('#breakpoints-list');
        container.innerHTML = '';

        if (this.breakpoints.size === 0) {
            container.innerHTML = '<div class="empty-state">No breakpoints set</div>';
            return;
        }

        this.breakpoints.forEach((bp, key) => {
            const item = document.createElement('div');
            item.className = 'breakpoint-item';
            if (!bp.enabled) item.classList.add('disabled');

            item.innerHTML = `
                <div class="breakpoint-info">
                    <div class="breakpoint-location">${bp.file}:${bp.line}</div>
                </div>
                <div class="breakpoint-actions">
                    <button class="breakpoint-action-btn toggle-btn" title="Toggle">
                        ${createIcon(bp.enabled ? 'toggle' : 'toggleOff', 12)}
                    </button>
                    <button class="breakpoint-action-btn remove-btn" title="Remove">
                        ${createIcon('trash', 12)}
                    </button>
                </div>
            `;

            const toggleBtn = item.querySelector('.toggle-btn');
            const removeBtn = item.querySelector('.remove-btn');

            toggleBtn.addEventListener('click', () => {
                bp.enabled = !bp.enabled;
                this.updateBreakpointsList();
            });

            removeBtn.addEventListener('click', () => {
                this.breakpoints.delete(key);
                this.updateBreakpointsList();
            });

            container.appendChild(item);
        });
    }

    // Watch Expressions
    showAddWatchModal() {
        this.container.querySelector('#add-watch-modal').classList.remove('hidden');
    }

    addWatchExpression() {
        const expression = this.container.querySelector('#watch-expression').value.trim();
        if (!expression) return;

        this.watchExpressions.push({
            expression,
            value: 'undefined',
            type: 'unknown'
        });

        this.updateWatchList();
        this.hideModal('add-watch-modal');
        this.showNotification(`Added watch: ${expression}`, 'success');
    }

    updateWatchList() {
        const container = this.container.querySelector('#watch-list');
        container.innerHTML = '';

        if (this.watchExpressions.length === 0) {
            container.innerHTML = '<div class="empty-state">No watch expressions</div>';
            return;
        }

        this.watchExpressions.forEach((watch, index) => {
            const item = document.createElement('div');
            item.className = 'watch-item';

            item.innerHTML = `
                <div class="watch-info">
                    <div class="watch-expression">${watch.expression}</div>
                    <div class="watch-value">${watch.value}</div>
                </div>
                <div class="watch-actions">
                    <button class="watch-action-btn remove-btn" title="Remove">
                        ${createIcon('trash', 12)}
                    </button>
                </div>
            `;

            const removeBtn = item.querySelector('.remove-btn');
            removeBtn.addEventListener('click', () => {
                this.watchExpressions.splice(index, 1);
                this.updateWatchList();
            });

            container.appendChild(item);
        });
    }

    // Testing Methods
    runAllTests() {
        this.showNotification('Running all tests...', 'info');
        this.updateTestControls(true);

        // Simulate test execution
        setTimeout(() => {
            this.simulateTestResults();
            this.updateTestControls(false);
            this.showNotification('All tests completed', 'success');
        }, 3000);
    }

    runFailedTests() {
        const failedTests = this.getFailedTests();
        if (failedTests.length === 0) {
            this.showNotification('No failed tests to run', 'info');
            return;
        }

        this.showNotification(`Running ${failedTests.length} failed tests...`, 'info');
        this.updateTestControls(true);

        setTimeout(() => {
            this.simulateTestResults();
            this.updateTestControls(false);
            this.showNotification('Failed tests completed', 'success');
        }, 2000);
    }

    debugTest() {
        this.showNotification('Starting test debugging...', 'info');
        this.startDebugging({ type: 'test' });
    }

    stopTests() {
        this.updateTestControls(false);
        this.showNotification('Tests stopped', 'info');
    }

    simulateTestResults() {
        // Randomly update test results
        this.testSuites.forEach(suite => {
            suite.tests.forEach(test => {
                if (Math.random() > 0.8) {
                    const statuses = ['passed', 'failed', 'skipped'];
                    test.status = statuses[Math.floor(Math.random() * statuses.length)];
                    test.duration = Math.floor(Math.random() * 200) + 10;

                    if (test.status === 'failed') {
                        test.error = 'Simulated test failure';
                    } else {
                        delete test.error;
                    }
                }
            });
        });

        this.updateTestDisplay();
        this.updateTestSummary();
    }

    updateTestControls(running) {
        const runBtns = this.container.querySelectorAll('#run-all-tests-btn, #run-failed-tests-btn, #debug-test-btn');
        const stopBtn = this.container.querySelector('#stop-tests-btn');

        runBtns.forEach(btn => btn.disabled = running);
        stopBtn.disabled = !running;
    }

    updateTestDisplay() {
        const container = this.container.querySelector('#test-suites');
        container.innerHTML = '';

        this.testSuites.forEach(suite => {
            const suiteElement = this.createTestSuiteElement(suite);
            container.appendChild(suiteElement);
        });
    }

    createTestSuiteElement(suite) {
        const element = document.createElement('div');
        element.className = 'test-suite';

        const passedCount = suite.tests.filter(t => t.status === 'passed').length;
        const failedCount = suite.tests.filter(t => t.status === 'failed').length;
        const skippedCount = suite.tests.filter(t => t.status === 'skipped').length;

        element.innerHTML = `
            <div class="suite-header">
                <div class="suite-info">
                    <div class="suite-name">${suite.name}</div>
                    <div class="suite-file">${suite.file}</div>
                </div>
                <div class="suite-stats">
                    <span class="stat passed">${passedCount}</span>
                    <span class="stat failed">${failedCount}</span>
                    <span class="stat skipped">${skippedCount}</span>
                </div>
            </div>
            <div class="suite-tests">
                ${suite.tests.map(test => this.createTestElement(test)).join('')}
            </div>
        `;

        return element;
    }

    createTestElement(test) {
        const statusIcon = this.getTestStatusIcon(test.status);
        const errorInfo = test.error ? `<div class="test-error">${test.error}</div>` : '';

        return `
            <div class="test-item ${test.status}">
                <div class="test-info">
                    <div class="test-status">${statusIcon}</div>
                    <div class="test-details">
                        <div class="test-name">${test.name}</div>
                        <div class="test-duration">${test.duration}ms</div>
                        ${errorInfo}
                    </div>
                </div>
                <div class="test-actions">
                    <button class="test-action-btn run-btn" title="Run Test">
                        ${createIcon('play', 12)}
                    </button>
                    <button class="test-action-btn debug-btn" title="Debug Test">
                        ${createIcon('debug', 12)}
                    </button>
                </div>
            </div>
        `;
    }

    getTestStatusIcon(status) {
        switch (status) {
            case 'passed':
                return createIcon('checkCircle', 14);
            case 'failed':
                return createIcon('errorCircle', 14);
            case 'skipped':
                return createIcon('skipCircle', 14);
            default:
                return createIcon('circle', 14);
        }
    }

    updateTestSummary() {
        const totalTests = this.testSuites.reduce((sum, suite) => sum + suite.tests.length, 0);
        const passedTests = this.testSuites.reduce((sum, suite) =>
            sum + suite.tests.filter(t => t.status === 'passed').length, 0);
        const failedTests = this.testSuites.reduce((sum, suite) =>
            sum + suite.tests.filter(t => t.status === 'failed').length, 0);
        const skippedTests = this.testSuites.reduce((sum, suite) =>
            sum + suite.tests.filter(t => t.status === 'skipped').length, 0);

        this.container.querySelector('#total-tests').textContent = totalTests;
        this.container.querySelector('#passed-tests').textContent = passedTests;
        this.container.querySelector('#failed-tests').textContent = failedTests;
        this.container.querySelector('#skipped-tests').textContent = skippedTests;
    }

    getFailedTests() {
        const failedTests = [];
        this.testSuites.forEach(suite => {
            suite.tests.forEach(test => {
                if (test.status === 'failed') {
                    failedTests.push({ suite: suite.name, test: test.name });
                }
            });
        });
        return failedTests;
    }

    filterTests(query) {
        // Filter tests by name
        const filtered = this.testSuites.map(suite => ({
            ...suite,
            tests: suite.tests.filter(test =>
                test.name.toLowerCase().includes(query.toLowerCase())
            )
        })).filter(suite => suite.tests.length > 0);

        this.displayFilteredTests(filtered);
    }

    filterTestsByStatus(status) {
        if (status === 'all') {
            this.updateTestDisplay();
            return;
        }

        const filtered = this.testSuites.map(suite => ({
            ...suite,
            tests: suite.tests.filter(test => test.status === status)
        })).filter(suite => suite.tests.length > 0);

        this.displayFilteredTests(filtered);
    }

    displayFilteredTests(filteredSuites) {
        const container = this.container.querySelector('#test-suites');
        container.innerHTML = '';

        filteredSuites.forEach(suite => {
            const suiteElement = this.createTestSuiteElement(suite);
            container.appendChild(suiteElement);
        });
    }

    // Profiler Methods
    startProfiler() {
        this.isProfiling = true;
        this.updateProfilerControls();
        this.simulateProfiler();
        this.showNotification('Profiler started', 'success');
    }

    stopProfiler() {
        this.isProfiling = false;
        this.updateProfilerControls();
        this.showNotification('Profiler stopped', 'info');
    }

    clearProfiler() {
        this.profilerData = null;
        this.updateProfilerDisplay();
        this.showNotification('Profiler data cleared', 'info');
    }

    simulateProfiler() {
        // Simulate profiler data collection
        const updateStats = () => {
            if (!this.isProfiling) return;

            const cpuUsage = Math.floor(Math.random() * 100);
            const memoryUsage = Math.floor(Math.random() * 512) + 128;
            const executionTime = Math.floor(Math.random() * 1000) + 100;

            this.container.querySelector('#cpu-usage').textContent = `${cpuUsage}%`;
            this.container.querySelector('#memory-usage').textContent = `${memoryUsage} MB`;
            this.container.querySelector('#execution-time').textContent = `${executionTime} ms`;

            setTimeout(updateStats, 1000);
        };

        updateStats();
        this.generateProfilerChart();
        this.updateHotspots();
    }

    updateProfilerControls() {
        const startBtn = this.container.querySelector('#start-profiler-btn');
        const stopBtn = this.container.querySelector('#stop-profiler-btn');

        startBtn.disabled = this.isProfiling;
        stopBtn.disabled = !this.isProfiling;
    }

    generateProfilerChart() {
        const chartContainer = this.container.querySelector('#profiler-chart');

        if (this.isProfiling) {
            chartContainer.innerHTML = `
                <div class="chart-active">
                    <div class="chart-line"></div>
                    <div class="chart-data">Real-time performance monitoring active</div>
                </div>
            `;
        } else {
            chartContainer.innerHTML = `
                <div class="chart-placeholder">
                    <div class="chart-icon">${createIcon('chart', 48)}</div>
                    <div class="chart-text">Start profiling to see performance data</div>
                </div>
            `;
        }
    }

    updateHotspots() {
        const container = this.container.querySelector('#hotspots-list');

        if (!this.isProfiling) {
            container.innerHTML = '<div class="empty-state">No profiling data available</div>';
            return;
        }

        const hotspots = [
            { function: 'processData', file: 'src/utils.js', time: '45.2ms', percentage: 23 },
            { function: 'renderComponent', file: 'src/components/App.js', time: '32.1ms', percentage: 16 },
            { function: 'validateInput', file: 'src/validation.js', time: '28.7ms', percentage: 14 },
            { function: 'fetchData', file: 'src/api.js', time: '21.3ms', percentage: 11 }
        ];

        container.innerHTML = hotspots.map(hotspot => `
            <div class="hotspot-item">
                <div class="hotspot-info">
                    <div class="hotspot-function">${hotspot.function}</div>
                    <div class="hotspot-file">${hotspot.file}</div>
                </div>
                <div class="hotspot-stats">
                    <div class="hotspot-time">${hotspot.time}</div>
                    <div class="hotspot-percentage">${hotspot.percentage}%</div>
                </div>
            </div>
        `).join('');
    }

    updateProfilerDisplay() {
        this.generateProfilerChart();
        this.updateHotspots();
    }

    // Coverage Methods
    runCoverage() {
        this.showNotification('Running code coverage analysis...', 'info');

        setTimeout(() => {
            this.simulateCoverageData();
            this.updateCoverageDisplay();
            this.showNotification('Coverage analysis completed', 'success');
        }, 2000);
    }

    clearCoverage() {
        this.coverageData = null;
        this.updateCoverageDisplay();
        this.showNotification('Coverage data cleared', 'info');
    }

    exportCoverage() {
        if (!this.coverageData) {
            this.showNotification('No coverage data to export', 'warning');
            return;
        }

        // Simulate export
        this.showNotification('Coverage report exported', 'success');
    }

    simulateCoverageData() {
        this.coverageData = {
            lineCoverage: Math.floor(Math.random() * 30) + 70,
            branchCoverage: Math.floor(Math.random() * 25) + 65,
            functionCoverage: Math.floor(Math.random() * 20) + 75,
            files: [
                { name: 'src/main.js', coverage: 85, lines: { covered: 42, total: 50 } },
                { name: 'src/utils.js', coverage: 92, lines: { covered: 23, total: 25 } },
                { name: 'src/api.js', coverage: 78, lines: { covered: 31, total: 40 } },
                { name: 'src/components/App.js', coverage: 88, lines: { covered: 44, total: 50 } }
            ]
        };
    }

    updateCoverageDisplay() {
        this.updateCoverageSummary();
        this.updateCoverageFiles();
    }

    updateCoverageSummary() {
        if (!this.coverageData) {
            this.container.querySelector('#line-coverage-circle .coverage-percentage').textContent = '0%';
            this.container.querySelector('#branch-coverage-circle .coverage-percentage').textContent = '0%';
            this.container.querySelector('#function-coverage-circle .coverage-percentage').textContent = '0%';
            return;
        }

        this.container.querySelector('#line-coverage-circle .coverage-percentage').textContent = `${this.coverageData.lineCoverage}%`;
        this.container.querySelector('#branch-coverage-circle .coverage-percentage').textContent = `${this.coverageData.branchCoverage}%`;
        this.container.querySelector('#function-coverage-circle .coverage-percentage').textContent = `${this.coverageData.functionCoverage}%`;

        // Update circle colors based on coverage
        this.updateCoverageCircle('line-coverage-circle', this.coverageData.lineCoverage);
        this.updateCoverageCircle('branch-coverage-circle', this.coverageData.branchCoverage);
        this.updateCoverageCircle('function-coverage-circle', this.coverageData.functionCoverage);
    }

    updateCoverageCircle(id, percentage) {
        const circle = this.container.querySelector(`#${id}`);
        circle.className = 'coverage-circle';

        if (percentage >= 80) {
            circle.classList.add('good');
        } else if (percentage >= 60) {
            circle.classList.add('medium');
        } else {
            circle.classList.add('poor');
        }
    }

    updateCoverageFiles() {
        const container = this.container.querySelector('#coverage-files-list');

        if (!this.coverageData || !this.coverageData.files) {
            container.innerHTML = '<div class="empty-state">No coverage data available</div>';
            return;
        }

        container.innerHTML = this.coverageData.files.map(file => `
            <div class="coverage-file-item">
                <div class="file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-lines">${file.lines.covered}/${file.lines.total} lines</div>
                </div>
                <div class="file-coverage ${this.getCoverageClass(file.coverage)}">
                    ${file.coverage}%
                </div>
            </div>
        `).join('');
    }

    getCoverageClass(percentage) {
        if (percentage >= 80) return 'good';
        if (percentage >= 60) return 'medium';
        return 'poor';
    }

    // Tab Management
    switchTab(tabName) {
        // Update tab buttons
        this.container.querySelectorAll('.debug-test-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        this.container.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab panels
        this.container.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        this.container.querySelector(`#${tabName}-panel`).classList.add('active');

        // Load tab-specific data
        switch (tabName) {
            case 'debugger':
                this.updateBreakpointsList();
                this.updateCallStack();
                this.updateVariables();
                this.updateWatchList();
                break;
            case 'testing':
                this.updateTestDisplay();
                this.updateTestSummary();
                break;
            case 'profiler':
                this.updateProfilerDisplay();
                break;
            case 'coverage':
                this.updateCoverageDisplay();
                break;
        }
    }

    // Utility Methods
    hideModal(modalId) {
        this.container.querySelector(`#${modalId}`).classList.add('hidden');

        // Clear form inputs
        const modal = this.container.querySelector(`#${modalId}`);
        const inputs = modal.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            if (input.type === 'checkbox') {
                input.checked = false;
            } else {
                input.value = '';
            }
        });
    }

    refreshDebugData() {
        this.updateBreakpointsList();
        this.updateCallStack();
        this.updateVariables();
        this.updateWatchList();
        this.updateTestDisplay();
        this.updateTestSummary();
        this.updateProfilerDisplay();
        this.updateCoverageDisplay();
    }

    showDebugSettings() {
        // Show debug settings modal
        this.showNotification('Debug settings not implemented yet', 'info');
    }

    saveTestConfiguration() {
        const framework = this.container.querySelector('#test-framework').value;
        const pattern = this.container.querySelector('#test-pattern').value;
        const timeout = this.container.querySelector('#test-timeout').value;
        const watchMode = this.container.querySelector('#test-watch-mode').checked;

        // Save configuration
        localStorage.setItem('rustcode-test-config', JSON.stringify({
            framework, pattern, timeout, watchMode
        }));

        this.hideModal('test-config-modal');
        this.showNotification('Test configuration saved', 'success');
    }

    showNotification(message, type = 'info') {
        console.log(`[Debug/Test ${type.toUpperCase()}] ${message}`);

        // If there's a status bar available, use it
        if (window.rustCodeApp && window.rustCodeApp.statusBar) {
            window.rustCodeApp.statusBar.showNotification(message, type);
        }
    }
}
