import * as monaco from 'monaco-editor';
import { CodeIntelligenceManager } from './code-intelligence.js';
import { registerCustomThemes } from '../themes/monaco-themes.js';
import { monacoErrorHandler } from '../utils/monaco-error-handler.js';

export class EditorManager {
    constructor(container, onContentChange) {
        this.container = container;
        this.onContentChange = onContentChange;
        this.editor = null;
        this.currentModel = null;
        this.isInitialized = false;
        this.codeIntelligence = null;
        this.models = new Map(); // Model cache for better performance
        this.disposables = []; // Track disposables for cleanup
        this.isLoading = false;
        this.loadingIndicator = null;
        this.performanceMetrics = {
            initTime: 0,
            modelCreationTime: 0,
            lastRenderTime: 0
        };
        this.settings = {
            autoSave: true,
            autoSaveDelay: 1000,
            enableCodeLens: true,
            enableInlayHints: true,
            enableSemanticHighlighting: true,
            enableAdvancedFeatures: true,
            maxModelSize: 10 * 1024 * 1024, // 10MB limit
            enablePerformanceMonitoring: true
        };
        this.autoSaveTimer = null;
        this.lastChangeTime = 0;

        this.init();
    }

    async init() {
        const startTime = performance.now();
        this.showLoadingIndicator();

        try {
            // Register custom themes
            registerCustomThemes();

            // Load settings from localStorage
            this.loadSettings();

            // Create Monaco editor instance with enhanced configuration
            this.editor = monaco.editor.create(this.container, {
                theme: 'rustcode-dark',
                fontSize: 14,
                fontFamily: 'Consolas, Monaco, "Courier New", monospace',
                lineNumbers: 'on',
                minimap: { enabled: true },
                scrollBeyondLastLine: false,
                automaticLayout: true,
                wordWrap: 'on',
                tabSize: 4,
                insertSpaces: true,
                detectIndentation: true,
                trimAutoWhitespace: true,
                renderWhitespace: 'selection',
                renderControlCharacters: false,
                renderIndentGuides: true,
                highlightActiveIndentGuide: true,
                bracketPairColorization: { enabled: true },
                guides: {
                    bracketPairs: true,
                    indentation: true
                },
                suggest: {
                    showKeywords: true,
                    showSnippets: true,
                    showFunctions: true,
                    showConstructors: true,
                    showFields: true,
                    showVariables: true,
                    showClasses: true,
                    showStructs: true,
                    showInterfaces: true,
                    showModules: true,
                    showProperties: true,
                    showEvents: true,
                    showOperators: true,
                    showUnits: true,
                    showValues: true,
                    showConstants: true,
                    showEnums: true,
                    showEnumMembers: true,
                    showColors: true,
                    showFiles: true,
                    showReferences: true,
                    showFolders: true,
                    showTypeParameters: true,
                    showUsers: true,
                    showIssues: true
                },
                quickSuggestions: {
                    other: true,
                    comments: false,
                    strings: false
                },
                parameterHints: { enabled: true },
                hover: { enabled: true },
                contextmenu: true,
                mouseWheelZoom: true,
                multiCursorModifier: 'ctrlCmd',
                accessibilitySupport: 'auto',
                find: {
                    seedSearchStringFromSelection: 'always',
                    autoFindInSelection: 'never'
                },
                // Enhanced performance settings
                scrollbar: {
                    useShadows: false,
                    verticalHasArrows: false,
                    horizontalHasArrows: false,
                    vertical: 'visible',
                    horizontal: 'visible',
                    verticalScrollbarSize: 10,
                    horizontalScrollbarSize: 10
                },
                // Advanced features
                codeLens: this.settings.enableCodeLens,
                inlayHints: { enabled: this.settings.enableInlayHints },
                semanticHighlighting: { enabled: this.settings.enableSemanticHighlighting },
                // Performance optimizations
                renderValidationDecorations: 'on',
                renderLineHighlight: 'line',
                renderLineHighlightOnlyWhenFocus: false,
                occurrencesHighlight: true,
                selectionHighlight: true,
                // Memory management
                model: null, // We'll set this manually for better control
                // Accessibility
                screenReaderAnnounceInlineSuggestion: true,
                accessibilityPageSize: 10
            });

            // Enhanced content change listener with performance optimization
            const contentChangeDisposable = this.editor.onDidChangeModelContent((e) => {
                this.lastChangeTime = Date.now();

                if (this.currentModel && this.onContentChange) {
                    const content = this.editor.getValue();
                    this.onContentChange(content);

                    // Auto-save functionality
                    if (this.settings.autoSave) {
                        this.scheduleAutoSave();
                    }

                    // Performance monitoring
                    if (this.settings.enablePerformanceMonitoring) {
                        this.monitorPerformance();
                    }
                }
            });
            this.disposables.push(contentChangeDisposable);

            // Set up cursor position change listener
            this.editor.onDidChangeCursorPosition((e) => {
                this.updateCursorPosition(e.position);
            });

            // Set up selection change listener
            this.editor.onDidChangeCursorSelection((e) => {
                this.updateSelection(e.selection);
            });

            // Initialize code intelligence with enhanced features
            this.codeIntelligence = new CodeIntelligenceManager(this);
            this.codeIntelligence.enhanceEditor(this.editor);

            // Setup advanced editor features
            this.setupAdvancedFeatures();

            // Setup keyboard shortcuts
            this.setupKeyboardShortcuts();

            // Setup context menu enhancements
            this.setupContextMenu();

            this.isInitialized = true;
            this.hideLoadingIndicator();

            // Record performance metrics
            this.performanceMetrics.initTime = performance.now() - startTime;

            console.log(`Monaco editor initialized with code intelligence (${this.performanceMetrics.initTime.toFixed(2)}ms)`);

            // Emit initialization event
            this.dispatchEvent('editor-initialized', {
                initTime: this.performanceMetrics.initTime,
                features: this.getEnabledFeatures()
            });

        } catch (error) {
            console.error('Failed to initialize Monaco editor:', error);
            this.hideLoadingIndicator();
            this.showError('Failed to initialize editor: ' + error.message);
        }
    }

    openFile(content, language = null) {
        if (!this.isInitialized) {
            console.error('Editor not initialized');
            return;
        }

        try {
            // Validate inputs
            if (typeof content !== 'string') {
                content = String(content || '');
            }

            // Dispose of the current model if it exists
            if (this.currentModel) {
                try {
                    this.currentModel.dispose();
                } catch (disposeError) {
                    console.warn('Error disposing previous model:', disposeError);
                }
                this.currentModel = null;
            }

            // Ensure language is valid
            if (!language || typeof language !== 'string') {
                language = 'plaintext';
            }

            // Create a new model with the file content
            const uri = monaco.Uri.file(`file-${Date.now()}.${this.getFileExtension(language)}`);

            // Try to create the model with error handling using the Monaco error handler
            this.currentModel = monacoErrorHandler.safeMonacoOperation(() => {
                try {
                    return monaco.editor.createModel(content, language, uri);
                } catch (modelError) {
                    console.warn('Failed to create model with language:', language, modelError);
                    // Fallback to plaintext if language-specific model creation fails
                    return monaco.editor.createModel(content, 'plaintext', uri);
                }
            }, null);

            // Set the model to the editor
            if (this.editor && this.currentModel) {
                this.editor.setModel(this.currentModel);

                // Focus the editor
                try {
                    this.editor.focus();
                } catch (focusError) {
                    console.warn('Failed to focus editor:', focusError);
                }

                // Reset cursor position
                try {
                    this.editor.setPosition({ lineNumber: 1, column: 1 });
                } catch (positionError) {
                    console.warn('Failed to set cursor position:', positionError);
                }

                console.log(`Opened file with language: ${language}`);
            } else {
                throw new Error('Editor or model is not available');
            }
        } catch (error) {
            console.error('Failed to open file in editor:', error);
            this.showError('Failed to open file: ' + error.message);

            // Try to create a fallback empty model
            try {
                if (this.editor && !this.currentModel) {
                    const fallbackUri = monaco.Uri.file(`fallback-${Date.now()}.txt`);
                    this.currentModel = monaco.editor.createModel('', 'plaintext', fallbackUri);
                    this.editor.setModel(this.currentModel);
                }
            } catch (fallbackError) {
                console.error('Failed to create fallback model:', fallbackError);
            }
        }
    }

    getValue() {
        if (!this.editor || !this.currentModel) {
            return '';
        }
        return this.editor.getValue();
    }

    setValue(content) {
        if (!this.editor || !this.currentModel) {
            return;
        }
        this.editor.setValue(content);
    }

    getLanguage() {
        if (!this.currentModel) {
            return null;
        }
        return this.currentModel.getLanguageId();
    }

    setLanguage(language) {
        if (!this.currentModel) {
            return;
        }
        monaco.editor.setModelLanguage(this.currentModel, language);
    }

    showFind() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('actions.find').run();
    }

    showReplace() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('editor.action.startFindReplaceAction').run();
    }

    format() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('editor.action.formatDocument').run();
    }

    undo() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('undo').run();
    }

    redo() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('redo').run();
    }

    cut() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('editor.action.clipboardCutAction').run();
    }

    copy() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('editor.action.clipboardCopyAction').run();
    }

    paste() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('editor.action.clipboardPasteAction').run();
    }

    selectAll() {
        if (!this.editor) {
            return;
        }
        this.editor.getAction('editor.action.selectAll').run();
    }

    getCursorPosition() {
        if (!this.editor) {
            return { lineNumber: 1, column: 1 };
        }
        return this.editor.getPosition();
    }

    setCursorPosition(lineNumber, column) {
        if (!this.editor) {
            return;
        }
        this.editor.setPosition({ lineNumber, column });
    }

    getSelection() {
        if (!this.editor) {
            return null;
        }
        return this.editor.getSelection();
    }

    setSelection(startLineNumber, startColumn, endLineNumber, endColumn) {
        if (!this.editor) {
            return;
        }
        this.editor.setSelection({
            startLineNumber,
            startColumn,
            endLineNumber,
            endColumn
        });
    }

    focus() {
        if (!this.editor) {
            return;
        }
        this.editor.focus();
    }

    resize() {
        if (!this.editor) {
            return;
        }
        this.editor.layout();
    }

    dispose() {
        if (this.currentModel) {
            this.currentModel.dispose();
            this.currentModel = null;
        }

        if (this.editor) {
            this.editor.dispose();
            this.editor = null;
        }

        this.isInitialized = false;
    }

    updateCursorPosition(position) {
        // Emit cursor position change event
        const event = new CustomEvent('cursorPositionChange', {
            detail: { line: position.lineNumber, column: position.column }
        });
        document.dispatchEvent(event);
    }

    updateSelection(selection) {
        // Emit selection change event
        const event = new CustomEvent('selectionChange', {
            detail: {
                startLine: selection.startLineNumber,
                startColumn: selection.startColumn,
                endLine: selection.endLineNumber,
                endColumn: selection.endColumn,
                selectedText: this.editor.getModel().getValueInRange(selection)
            }
        });
        document.dispatchEvent(event);
    }

    getFileExtension(language) {
        const extensionMap = {
            'rust': 'rs',
            'javascript': 'js',
            'typescript': 'ts',
            'python': 'py',
            'html': 'html',
            'css': 'css',
            'json': 'json',
            'xml': 'xml',
            'markdown': 'md',
            'yaml': 'yml',
            'toml': 'toml',
            'shell': 'sh',
            'c': 'c',
            'cpp': 'cpp',
            'java': 'java',
            'go': 'go',
            'php': 'php',
            'ruby': 'rb',
            'swift': 'swift',
            'kotlin': 'kt',
            'scala': 'scala',
            'sql': 'sql',
            'dockerfile': 'dockerfile'
        };

        return extensionMap[language] || 'txt';
    }

    showError(message) {
        console.error(message);
        // TODO: Show error in UI
    }

    // Enhanced methods for performance and features
    showLoadingIndicator() {
        if (this.loadingIndicator) return;

        this.loadingIndicator = document.createElement('div');
        this.loadingIndicator.className = 'editor-loading-indicator';
        this.loadingIndicator.innerHTML = `
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading editor...</div>
        `;
        this.container.appendChild(this.loadingIndicator);
    }

    hideLoadingIndicator() {
        if (this.loadingIndicator) {
            this.loadingIndicator.remove();
            this.loadingIndicator = null;
        }
    }

    loadSettings() {
        const savedSettings = localStorage.getItem('editor-settings');
        if (savedSettings) {
            try {
                const parsed = JSON.parse(savedSettings);
                this.settings = { ...this.settings, ...parsed };
            } catch (error) {
                console.warn('Failed to load editor settings:', error);
            }
        }
    }

    saveSettings() {
        localStorage.setItem('editor-settings', JSON.stringify(this.settings));
    }

    setupAdvancedFeatures() {
        if (!this.settings.enableAdvancedFeatures) return;

        // Setup code folding
        this.editor.updateOptions({
            folding: true,
            foldingStrategy: 'indentation',
            foldingHighlight: true,
            unfoldOnClickAfterEndOfLine: true
        });

        // Setup bracket matching
        this.editor.updateOptions({
            matchBrackets: 'always',
            renderWhitespace: 'boundary'
        });

        // Setup word highlighting
        this.editor.updateOptions({
            occurrencesHighlight: true,
            selectionHighlight: true
        });
    }

    setupKeyboardShortcuts() {
        // Enhanced keyboard shortcuts
        this.editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
            this.triggerAutoSave();
        });

        this.editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyF, () => {
            this.format();
        });

        this.editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyD, () => {
            this.duplicateLine();
        });

        this.editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyK, () => {
            this.deleteLine();
        });
    }

    setupContextMenu() {
        // Enhanced context menu
        this.editor.addAction({
            id: 'format-document',
            label: 'Format Document',
            contextMenuGroupId: 'modification',
            contextMenuOrder: 1,
            run: () => this.format()
        });

        this.editor.addAction({
            id: 'duplicate-line',
            label: 'Duplicate Line',
            contextMenuGroupId: 'modification',
            contextMenuOrder: 2,
            run: () => this.duplicateLine()
        });

        this.editor.addAction({
            id: 'delete-line',
            label: 'Delete Line',
            contextMenuGroupId: 'modification',
            contextMenuOrder: 3,
            run: () => this.deleteLine()
        });
    }

    setupModelFeatures(model, language) {
        // Setup language-specific features
        if (language === 'javascript' || language === 'typescript') {
            this.setupJavaScriptFeatures(model);
        } else if (language === 'rust') {
            this.setupRustFeatures(model);
        } else if (language === 'python') {
            this.setupPythonFeatures(model);
        }

        // Setup common features for all models
        this.setupCommonModelFeatures(model);
    }

    setupJavaScriptFeatures(model) {
        // JavaScript-specific enhancements
        // This could include ESLint integration, TypeScript checking, etc.
    }

    setupRustFeatures(model) {
        // Rust-specific enhancements
        // This could include cargo integration, rust-analyzer features, etc.
    }

    setupPythonFeatures(model) {
        // Python-specific enhancements
        // This could include pylint integration, type checking, etc.
    }

    setupCommonModelFeatures(model) {
        // Common features for all models
        model.onDidChangeContent(() => {
            this.lastChangeTime = Date.now();
        });
    }

    scheduleAutoSave() {
        if (this.autoSaveTimer) {
            clearTimeout(this.autoSaveTimer);
        }

        this.autoSaveTimer = setTimeout(() => {
            this.triggerAutoSave();
        }, this.settings.autoSaveDelay);
    }

    triggerAutoSave() {
        if (!this.settings.autoSave) return;

        const content = this.getValue();
        const event = new CustomEvent('auto-save', {
            detail: { content, timestamp: Date.now() }
        });
        document.dispatchEvent(event);
    }

    monitorPerformance() {
        const now = performance.now();
        this.performanceMetrics.lastRenderTime = now;

        // Monitor memory usage
        if (performance.memory) {
            const memoryInfo = {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            };

            // Warn if memory usage is high
            if (memoryInfo.used / memoryInfo.limit > 0.8) {
                console.warn('High memory usage detected:', memoryInfo);
                this.optimizeMemoryUsage();
            }
        }
    }

    optimizeMemoryUsage() {
        // Clean up old models
        const maxCachedModels = 10;
        if (this.models.size > maxCachedModels) {
            const modelsArray = Array.from(this.models.entries());
            const oldestModels = modelsArray.slice(0, this.models.size - maxCachedModels);

            oldestModels.forEach(([key, model]) => {
                if (model !== this.currentModel) {
                    model.dispose();
                    this.models.delete(key);
                }
            });
        }
    }

    isModelCached(model) {
        return Array.from(this.models.values()).includes(model);
    }

    setModel(model) {
        if (this.editor && model) {
            this.currentModel = model;
            this.editor.setModel(model);
            this.editor.focus();
        }
    }

    detectLanguage(content, filePath = null) {
        // Enhanced language detection
        if (filePath) {
            const extension = filePath.split('.').pop()?.toLowerCase();
            const languageMap = {
                'rs': 'rust',
                'js': 'javascript',
                'ts': 'typescript',
                'py': 'python',
                'html': 'html',
                'css': 'css',
                'json': 'json',
                'xml': 'xml',
                'md': 'markdown',
                'yml': 'yaml',
                'yaml': 'yaml',
                'toml': 'toml',
                'sh': 'shell',
                'bash': 'shell',
                'c': 'c',
                'cpp': 'cpp',
                'h': 'c',
                'hpp': 'cpp',
                'java': 'java',
                'go': 'go',
                'php': 'php',
                'rb': 'ruby',
                'swift': 'swift',
                'kt': 'kotlin',
                'scala': 'scala',
                'sql': 'sql'
            };

            if (languageMap[extension]) {
                return languageMap[extension];
            }
        }

        // Content-based detection
        if (content.includes('fn main()') || content.includes('use std::')) {
            return 'rust';
        } else if (content.includes('function') || content.includes('const ') || content.includes('let ')) {
            return 'javascript';
        } else if (content.includes('def ') || content.includes('import ')) {
            return 'python';
        } else if (content.includes('<html') || content.includes('<!DOCTYPE')) {
            return 'html';
        } else if (content.includes('{') && content.includes('}') && content.includes(':')) {
            return 'json';
        }

        return 'plaintext';
    }

    duplicateLine() {
        const position = this.editor.getPosition();
        const model = this.editor.getModel();
        const lineContent = model.getLineContent(position.lineNumber);

        this.editor.executeEdits('duplicate-line', [{
            range: {
                startLineNumber: position.lineNumber,
                startColumn: model.getLineMaxColumn(position.lineNumber),
                endLineNumber: position.lineNumber,
                endColumn: model.getLineMaxColumn(position.lineNumber)
            },
            text: '\n' + lineContent
        }]);
    }

    deleteLine() {
        const position = this.editor.getPosition();
        const model = this.editor.getModel();

        this.editor.executeEdits('delete-line', [{
            range: {
                startLineNumber: position.lineNumber,
                startColumn: 1,
                endLineNumber: position.lineNumber + 1,
                endColumn: 1
            },
            text: ''
        }]);
    }

    getEnabledFeatures() {
        return {
            codeIntelligence: !!this.codeIntelligence,
            autoSave: this.settings.autoSave,
            codeLens: this.settings.enableCodeLens,
            inlayHints: this.settings.enableInlayHints,
            semanticHighlighting: this.settings.enableSemanticHighlighting,
            advancedFeatures: this.settings.enableAdvancedFeatures,
            performanceMonitoring: this.settings.enablePerformanceMonitoring
        };
    }

    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }

    // Enhanced dispose method
    dispose() {
        // Clear timers
        if (this.autoSaveTimer) {
            clearTimeout(this.autoSaveTimer);
            this.autoSaveTimer = null;
        }

        // Dispose all cached models
        this.models.forEach(model => {
            if (model && !model.isDisposed()) {
                model.dispose();
            }
        });
        this.models.clear();

        // Dispose current model
        if (this.currentModel && !this.currentModel.isDisposed()) {
            this.currentModel.dispose();
            this.currentModel = null;
        }

        // Dispose all event listeners
        this.disposables.forEach(disposable => {
            if (disposable && typeof disposable.dispose === 'function') {
                disposable.dispose();
            }
        });
        this.disposables.length = 0;

        // Dispose code intelligence
        if (this.codeIntelligence && typeof this.codeIntelligence.dispose === 'function') {
            this.codeIntelligence.dispose();
            this.codeIntelligence = null;
        }

        // Dispose editor
        if (this.editor) {
            this.editor.dispose();
            this.editor = null;
        }

        // Clean up loading indicator
        this.hideLoadingIndicator();

        this.isInitialized = false;
        console.log('Editor disposed successfully');
    }
}
