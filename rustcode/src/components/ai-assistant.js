import { createIcon } from '../icons/icon-library.js';

export class AIAssistant {
    constructor(container, editorManager) {
        this.container = container;
        this.editorManager = editorManager;
        this.isVisible = false;
        this.apiKey = localStorage.getItem('gemini-api-key') || '';
        this.conversationHistory = [];
        this.isProcessing = false;
        this.agenticMode = localStorage.getItem('ai-agentic-mode') === 'true';
        this.currentTask = null;
        this.taskQueue = [];
        this.autonomousMode = localStorage.getItem('ai-autonomous-mode') === 'true';
        this.codeAnalysisMode = localStorage.getItem('ai-code-analysis') === 'true';
        this.autoRefactorMode = localStorage.getItem('ai-auto-refactor') === 'true';
        this.smartSuggestionsMode = localStorage.getItem('ai-smart-suggestions') === 'true';
        this.continuousLearningMode = localStorage.getItem('ai-continuous-learning') === 'true';
        this.workflowAutomation = localStorage.getItem('ai-workflow-automation') === 'true';
        this.contextMemory = JSON.parse(localStorage.getItem('ai-context-memory') || '[]');
        this.codePatterns = JSON.parse(localStorage.getItem('ai-code-patterns') || '{}');
        this.userPreferences = JSON.parse(localStorage.getItem('ai-user-preferences') || '{}');
        this.projectContext = JSON.parse(localStorage.getItem('ai-project-context') || '{}');
        this.activeAgents = new Map();
        this.agentCapabilities = [
            'code-generation', 'code-review', 'debugging', 'testing',
            'refactoring', 'documentation', 'optimization', 'security-analysis'
        ];

        this.init();
    }

    init() {
        this.createUI();
        this.setupEventListeners();
        this.loadSettings();
        this.initializeAgents();
        this.startBackgroundProcesses();
        this.loadProjectContext();
    }

    createUI() {
        this.container.innerHTML = `
            <div class="ai-assistant-panel">
                <div class="ai-header">
                    <div class="ai-title">
                        ${createIcon('ai', 16)} AI Assistant
                    </div>
                    <div class="ai-controls">
                        <button class="ai-btn" id="ai-settings-btn" title="Settings">
                            ${createIcon('settings', 14)}
                        </button>
                        <button class="ai-btn" id="ai-clear-btn" title="Clear Chat">
                            ${createIcon('clear', 14)}
                        </button>
                        <button class="ai-btn" id="ai-close-btn" title="Close">
                            ${createIcon('close', 14)}
                        </button>
                    </div>
                </div>

                <div class="ai-mode-selector">
                    <button class="mode-btn active" data-mode="chat">Chat</button>
                    <button class="mode-btn" data-mode="agentic">Agentic</button>
                    <button class="mode-btn" data-mode="code-gen">Code Gen</button>
                    <button class="mode-btn" data-mode="refactor">Refactor</button>
                    <button class="mode-btn" data-mode="debug">Debug</button>
                </div>

                <div class="ai-agents-panel hidden" id="ai-agents-panel">
                    <h4>Active Agents</h4>
                    <div class="agents-list" id="agents-list">
                        <div class="empty-state">No active agents</div>
                    </div>
                    <div class="agent-controls">
                        <button class="btn-small" id="spawn-agent-btn">Spawn Agent</button>
                        <button class="btn-small" id="terminate-all-agents-btn">Terminate All</button>
                    </div>
                </div>

                <div class="ai-chat-container">
                    <div class="ai-messages" id="ai-messages"></div>
                    <div class="ai-input-container">
                        <div class="ai-input-wrapper">
                            <textarea
                                id="ai-input"
                                placeholder="Ask AI anything about your code..."
                                rows="3"
                            ></textarea>
                            <div class="ai-input-actions">
                                <button class="ai-send-btn" id="ai-send-btn" disabled>
                                    ${createIcon('send', 16)}
                                </button>
                                <button class="ai-attach-btn" id="ai-attach-btn" title="Attach current file">
                                    ${createIcon('attach', 16)}
                                </button>
                                <button class="ai-agents-btn" id="ai-agents-btn" title="Toggle Agents Panel">
                                    ${createIcon('agents', 16)}
                                    <span class="agent-count" id="agent-count">${this.activeAgents.size}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ai-status" id="ai-status">
                    <span class="status-text">Ready</span>
                    <div class="status-indicator"></div>
                </div>

                <div class="ai-settings-modal hidden" id="ai-settings-modal">
                    <div class="settings-content">
                        <h3>AI Assistant Settings</h3>
                        <div class="setting-group">
                            <label for="gemini-api-key">Gemini API Key:</label>
                            <input type="password" id="gemini-api-key" placeholder="Enter your Gemini API key">
                            <small>Get your API key from <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a></small>
                        </div>
                        <div class="setting-group">
                            <label for="ai-model">Model:</label>
                            <select id="ai-model">
                                <option value="gemini-2.0-flash-exp">Gemini 2.0 Flash (Experimental)</option>
                                <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                                <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                            </select>
                        </div>
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="agentic-auto-execute">
                                Auto-execute agentic suggestions
                            </label>
                        </div>
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="context-aware">
                                Include file context in requests
                            </label>
                        </div>
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="autonomous-mode-setting">
                                Autonomous mode
                            </label>
                        </div>
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="code-analysis-setting">
                                Real-time code analysis
                            </label>
                        </div>
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="continuous-learning-setting">
                                Continuous learning
                            </label>
                        </div>
                        <div class="setting-group">
                            <label>
                                <input type="checkbox" id="workflow-automation-setting">
                                Workflow automation
                            </label>
                        </div>
                        <div class="settings-actions">
                            <button class="btn-primary" id="save-ai-settings">Save</button>
                            <button class="btn-secondary" id="cancel-ai-settings">Cancel</button>
                        </div>
                    </div>
                </div>

                <!-- Agent Spawn Modal -->
                <div class="ai-modal hidden" id="agent-spawn-modal">
                    <div class="modal-content">
                        <h3>Spawn AI Agent</h3>
                        <div class="form-group">
                            <label for="agent-type">Agent Type:</label>
                            <select id="agent-type">
                                ${this.agentCapabilities.map(cap =>
                                    `<option value="${cap}">${cap.replace('-', ' ').toUpperCase()}</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="agent-task">Task Description:</label>
                            <textarea id="agent-task" placeholder="Describe what this agent should do..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="agent-priority">Priority:</label>
                            <select id="agent-priority">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                                <option value="critical">Critical</option>
                            </select>
                        </div>
                        <div class="modal-actions">
                            <button class="btn-primary" id="spawn-agent-confirm-btn">Spawn Agent</button>
                            <button class="btn-secondary" id="cancel-agent-spawn-btn">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    setupEventListeners() {
        // Mode selector
        this.container.querySelectorAll('.mode-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setMode(e.target.dataset.mode);
            });
        });

        // Input handling
        const input = this.container.querySelector('#ai-input');
        const sendBtn = this.container.querySelector('#ai-send-btn');

        input.addEventListener('input', () => {
            sendBtn.disabled = !input.value.trim();
        });

        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        sendBtn.addEventListener('click', () => this.sendMessage());

        // Control buttons
        this.container.querySelector('#ai-settings-btn').addEventListener('click', () => {
            this.showSettings();
        });

        this.container.querySelector('#ai-clear-btn').addEventListener('click', () => {
            this.clearChat();
        });

        this.container.querySelector('#ai-close-btn').addEventListener('click', () => {
            this.hide();
        });

        this.container.querySelector('#ai-attach-btn').addEventListener('click', () => {
            this.attachCurrentFile();
        });

        this.container.querySelector('#ai-agents-btn').addEventListener('click', () => {
            this.toggleAgentsPanel();
        });

        // Agent controls
        this.container.querySelector('#spawn-agent-btn').addEventListener('click', () => {
            this.showAgentSpawnModal();
        });

        this.container.querySelector('#terminate-all-agents-btn').addEventListener('click', () => {
            this.terminateAllAgents();
        });

        // Settings modal
        this.container.querySelector('#save-ai-settings').addEventListener('click', () => {
            this.saveSettings();
        });

        this.container.querySelector('#cancel-ai-settings').addEventListener('click', () => {
            this.hideSettings();
        });

        // Agent spawn modal
        this.container.querySelector('#spawn-agent-confirm-btn').addEventListener('click', () => {
            this.confirmSpawnAgent();
        });

        this.container.querySelector('#cancel-agent-spawn-btn').addEventListener('click', () => {
            this.hideAgentSpawnModal();
        });
    }

    setMode(mode) {
        this.container.querySelectorAll('.mode-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        this.container.querySelector(`[data-mode="${mode}"]`).classList.add('active');

        this.currentMode = mode;
        this.agenticMode = mode === 'agentic';

        // Update placeholder based on mode
        const input = this.container.querySelector('#ai-input');
        const placeholders = {
            chat: 'Ask AI anything about your code...',
            agentic: 'Describe what you want me to build or fix...',
            'code-gen': 'Describe the code you want to generate...',
            refactor: 'Describe how you want to refactor this code...',
            debug: 'Describe the bug or issue you\'re facing...'
        };
        input.placeholder = placeholders[mode] || placeholders.chat;
    }

    async sendMessage() {
        const input = this.container.querySelector('#ai-input');
        const message = input.value.trim();

        if (!message || this.isProcessing) return;

        if (!this.apiKey) {
            this.showError('Please set your Gemini API key in settings first.');
            this.showSettings();
            return;
        }

        this.isProcessing = true;
        this.updateStatus('Processing...', 'processing');

        // Add user message to chat
        this.addMessage('user', message);
        input.value = '';
        input.disabled = true;

        try {
            const response = await this.callGeminiAPI(message);
            this.addMessage('assistant', response);

            // Handle agentic mode
            if (this.agenticMode) {
                await this.handleAgenticResponse(response);
            }

        } catch (error) {
            console.error('AI request failed:', error);
            this.addMessage('error', `Error: ${error.message}`);
            this.showError('Failed to get AI response. Please check your API key and try again.');
        } finally {
            this.isProcessing = false;
            this.updateStatus('Ready', 'ready');
            input.disabled = false;
            input.focus();
        }
    }

    async callGeminiAPI(message) {
        const model = localStorage.getItem('ai-model') || 'gemini-2.0-flash-exp';
        const contextAware = localStorage.getItem('context-aware') === 'true';

        let prompt = message;

        // Add context if enabled
        if (contextAware && this.editorManager.editor) {
            const currentFile = this.getCurrentFileContext();
            if (currentFile) {
                prompt = `Context: I'm working on a file "${currentFile.name}" with the following content:\n\`\`\`${currentFile.language}\n${currentFile.content}\n\`\`\`\n\nUser request: ${message}`;
            }
        }

        // Add mode-specific system prompts
        const systemPrompts = {
            agentic: 'You are an autonomous coding assistant. Provide step-by-step implementation plans and code solutions. Be specific about file changes needed.',
            'code-gen': 'You are a code generation specialist. Generate clean, well-documented code based on requirements.',
            refactor: 'You are a code refactoring expert. Suggest improvements while maintaining functionality.',
            debug: 'You are a debugging expert. Analyze code issues and provide solutions.'
        };

        if (systemPrompts[this.currentMode]) {
            prompt = `${systemPrompts[this.currentMode]}\n\n${prompt}`;
        }

        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${this.apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.7,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 8192,
                }
            })
        });

        if (!response.ok) {
            throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
            throw new Error('Invalid response from Gemini API');
        }

        return data.candidates[0].content.parts[0].text;
    }

    async handleAgenticResponse(response) {
        // Parse agentic commands from response
        const codeBlocks = this.extractCodeBlocks(response);
        const fileOperations = this.extractFileOperations(response);

        if (codeBlocks.length > 0 || fileOperations.length > 0) {
            const autoExecute = localStorage.getItem('agentic-auto-execute') === 'true';

            if (autoExecute) {
                await this.executeAgenticActions(codeBlocks, fileOperations);
            } else {
                this.showAgenticActions(codeBlocks, fileOperations);
            }
        }
    }

    extractCodeBlocks(text) {
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
        const blocks = [];
        let match;

        while ((match = codeBlockRegex.exec(text)) !== null) {
            blocks.push({
                language: match[1] || 'text',
                code: match[2].trim()
            });
        }

        return blocks;
    }

    extractFileOperations(text) {
        // Extract file operations like "create file", "modify file", etc.
        const operations = [];
        const lines = text.split('\n');

        for (const line of lines) {
            if (line.toLowerCase().includes('create file') || line.toLowerCase().includes('new file')) {
                const fileMatch = line.match(/["`']([^"`']+\.[a-zA-Z]+)["`']/);
                if (fileMatch) {
                    operations.push({
                        type: 'create',
                        file: fileMatch[1]
                    });
                }
            }
        }

        return operations;
    }

    getCurrentFileContext() {
        if (!this.editorManager.editor) return null;

        const model = this.editorManager.editor.getModel();
        if (!model) return null;

        // Get current tab info from the app
        const app = window.rustCodeApp;
        if (!app || !app.tabManager) return null;

        const activeTab = app.tabManager.getActiveTab();
        if (!activeTab) return null;

        return {
            name: activeTab.file_name,
            content: model.getValue(),
            language: model.getLanguageId()
        };
    }

    addMessage(type, content) {
        const messagesContainer = this.container.querySelector('#ai-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `ai-message ${type}`;

        const timestamp = new Date().toLocaleTimeString();

        if (type === 'user') {
            messageDiv.innerHTML = `
                <div class="message-header">
                    <span class="message-sender">You</span>
                    <span class="message-time">${timestamp}</span>
                </div>
                <div class="message-content">${this.escapeHtml(content)}</div>
            `;
        } else if (type === 'assistant') {
            messageDiv.innerHTML = `
                <div class="message-header">
                    <span class="message-sender">${createIcon('ai', 14)} AI Assistant</span>
                    <span class="message-time">${timestamp}</span>
                </div>
                <div class="message-content">${this.formatAIResponse(content)}</div>
            `;
        } else if (type === 'error') {
            messageDiv.innerHTML = `
                <div class="message-header">
                    <span class="message-sender">${createIcon('error', 14)} Error</span>
                    <span class="message-time">${timestamp}</span>
                </div>
                <div class="message-content">${this.escapeHtml(content)}</div>
            `;
        }

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        this.conversationHistory.push({ type, content, timestamp });
    }

    formatAIResponse(content) {
        // Convert markdown-like formatting to HTML
        let formatted = this.escapeHtml(content);

        // Code blocks
        formatted = formatted.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
            return `<div class="code-block">
                <div class="code-header">
                    <span class="code-lang">${lang || 'text'}</span>
                    <button class="copy-code-btn" onclick="navigator.clipboard.writeText(\`${code.replace(/`/g, '\\`')}\`)">
                        ${createIcon('copy', 12)}
                    </button>
                </div>
                <pre><code>${code}</code></pre>
            </div>`;
        });

        // Inline code
        formatted = formatted.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');

        // Bold text
        formatted = formatted.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');

        // Italic text
        formatted = formatted.replace(/\*([^*]+)\*/g, '<em>$1</em>');

        // Line breaks
        formatted = formatted.replace(/\n/g, '<br>');

        return formatted;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    show() {
        this.isVisible = true;
        this.container.style.display = 'flex';
        this.container.querySelector('#ai-input').focus();
    }

    hide() {
        this.isVisible = false;
        this.container.style.display = 'none';
    }

    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    showSettings() {
        const modal = this.container.querySelector('#ai-settings-modal');
        modal.classList.remove('hidden');

        // Load current settings
        this.container.querySelector('#gemini-api-key').value = this.apiKey;
        this.container.querySelector('#ai-model').value = localStorage.getItem('ai-model') || 'gemini-2.0-flash-exp';
        this.container.querySelector('#agentic-auto-execute').checked = localStorage.getItem('agentic-auto-execute') === 'true';
        this.container.querySelector('#context-aware').checked = localStorage.getItem('context-aware') === 'true';
    }

    hideSettings() {
        this.container.querySelector('#ai-settings-modal').classList.add('hidden');
    }

    saveSettings() {
        this.apiKey = this.container.querySelector('#gemini-api-key').value;
        const model = this.container.querySelector('#ai-model').value;
        const autoExecute = this.container.querySelector('#agentic-auto-execute').checked;
        const contextAware = this.container.querySelector('#context-aware').checked;
        const autonomousMode = this.container.querySelector('#autonomous-mode-setting').checked;
        const codeAnalysis = this.container.querySelector('#code-analysis-setting').checked;
        const continuousLearning = this.container.querySelector('#continuous-learning-setting').checked;
        const workflowAutomation = this.container.querySelector('#workflow-automation-setting').checked;

        localStorage.setItem('gemini-api-key', this.apiKey);
        localStorage.setItem('ai-model', model);
        localStorage.setItem('agentic-auto-execute', autoExecute);
        localStorage.setItem('context-aware', contextAware);
        localStorage.setItem('ai-autonomous-mode', autonomousMode);
        localStorage.setItem('ai-code-analysis', codeAnalysis);
        localStorage.setItem('ai-continuous-learning', continuousLearning);
        localStorage.setItem('ai-workflow-automation', workflowAutomation);

        // Update instance variables
        this.autonomousMode = autonomousMode;
        this.codeAnalysisMode = codeAnalysis;
        this.continuousLearningMode = continuousLearning;
        this.workflowAutomation = workflowAutomation;

        // Restart background processes if needed
        this.startBackgroundProcesses();

        this.hideSettings();
        this.updateStatus('Settings saved', 'success');

        setTimeout(() => {
            this.updateStatus('Ready', 'ready');
        }, 2000);
    }

    loadSettings() {
        this.apiKey = localStorage.getItem('gemini-api-key') || '';
    }

    clearChat() {
        this.container.querySelector('#ai-messages').innerHTML = '';
        this.conversationHistory = [];
    }

    attachCurrentFile() {
        const context = this.getCurrentFileContext();
        if (!context) {
            this.showError('No file is currently open in the editor.');
            return;
        }

        const input = this.container.querySelector('#ai-input');
        const currentValue = input.value;
        const attachment = `\n\n[Attached: ${context.name}]\n`;
        input.value = currentValue + attachment;
        input.focus();
    }

    updateStatus(text, type = 'ready') {
        const statusElement = this.container.querySelector('#ai-status .status-text');
        const indicator = this.container.querySelector('#ai-status .status-indicator');

        statusElement.textContent = text;
        indicator.className = `status-indicator ${type}`;
    }

    showError(message) {
        this.updateStatus(message, 'error');
        setTimeout(() => {
            this.updateStatus('Ready', 'ready');
        }, 5000);
    }

    // New UI methods for enhanced features
    toggleAgentsPanel() {
        const agentsPanel = this.container.querySelector('#ai-agents-panel');
        agentsPanel.classList.toggle('hidden');
    }

    showAgentSpawnModal() {
        const modal = this.container.querySelector('#agent-spawn-modal');
        modal.classList.remove('hidden');
    }

    hideAgentSpawnModal() {
        const modal = this.container.querySelector('#agent-spawn-modal');
        modal.classList.add('hidden');
    }

    confirmSpawnAgent() {
        const type = this.container.querySelector('#agent-type').value;
        const task = this.container.querySelector('#agent-task').value;
        const priority = this.container.querySelector('#agent-priority').value;

        if (!task.trim()) {
            this.showError('Please provide a task description for the agent.');
            return;
        }

        this.spawnAgent(type, task, priority);
        this.hideAgentSpawnModal();

        // Clear form
        this.container.querySelector('#agent-task').value = '';
        this.container.querySelector('#agent-priority').value = 'medium';
    }

    // Advanced Agentic Features
    initializeAgents() {
        // Initialize default agents based on capabilities
        this.agentCapabilities.forEach(capability => {
            if (localStorage.getItem(`agent-${capability}-enabled`) === 'true') {
                this.spawnAgent(capability, `Default ${capability} agent`, 'medium');
            }
        });
    }

    startBackgroundProcesses() {
        if (this.autonomousMode) {
            // Start continuous code analysis
            this.startCodeAnalysis();
        }

        if (this.continuousLearningMode) {
            // Start learning from user patterns
            this.startLearningProcess();
        }

        if (this.workflowAutomation) {
            // Start workflow monitoring
            this.startWorkflowMonitoring();
        }
    }

    loadProjectContext() {
        // Load project-specific context and patterns
        const projectData = this.getProjectData();
        if (projectData) {
            this.projectContext = projectData;
            this.updateContextMemory('project', projectData);
        }
    }

    spawnAgent(type, task, priority = 'medium') {
        const agentId = `agent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const agent = {
            id: agentId,
            type: type,
            task: task,
            priority: priority,
            status: 'active',
            created: new Date(),
            progress: 0,
            results: []
        };

        this.activeAgents.set(agentId, agent);
        this.updateAgentDisplay();

        // Start agent execution
        this.executeAgent(agent);

        return agentId;
    }

    async executeAgent(agent) {
        try {
            agent.status = 'working';
            this.updateAgentDisplay();

            // Simulate agent work based on type
            const result = await this.performAgentTask(agent);

            agent.results.push(result);
            agent.progress = 100;
            agent.status = 'completed';

            this.updateAgentDisplay();
            this.notifyAgentCompletion(agent, result);

        } catch (error) {
            agent.status = 'error';
            agent.error = error.message;
            this.updateAgentDisplay();
            console.error(`Agent ${agent.id} failed:`, error);
        }
    }

    async performAgentTask(agent) {
        // Simulate different agent capabilities
        switch (agent.type) {
            case 'code-generation':
                return await this.generateCode(agent.task);
            case 'code-review':
                return await this.reviewCode(agent.task);
            case 'debugging':
                return await this.debugCode(agent.task);
            case 'testing':
                return await this.generateTests(agent.task);
            case 'refactoring':
                return await this.refactorCode(agent.task);
            case 'documentation':
                return await this.generateDocumentation(agent.task);
            case 'optimization':
                return await this.optimizeCode(agent.task);
            case 'security-analysis':
                return await this.analyzeSecurityIssues(agent.task);
            default:
                return { message: 'Agent task completed', details: agent.task };
        }
    }

    updateAgentDisplay() {
        const agentsList = this.container.querySelector('#agents-list');
        const agentCount = this.container.querySelector('#agent-count');

        if (agentCount) {
            agentCount.textContent = this.activeAgents.size;
        }

        if (agentsList) {
            if (this.activeAgents.size === 0) {
                agentsList.innerHTML = '<div class="empty-state">No active agents</div>';
            } else {
                agentsList.innerHTML = Array.from(this.activeAgents.values()).map(agent => `
                    <div class="agent-item ${agent.status}">
                        <div class="agent-header">
                            <span class="agent-type">${agent.type}</span>
                            <span class="agent-status">${agent.status}</span>
                        </div>
                        <div class="agent-task">${agent.task}</div>
                        <div class="agent-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${agent.progress}%"></div>
                            </div>
                        </div>
                        <button class="terminate-agent-btn" onclick="window.rustCodeApp.aiAssistant.terminateAgent('${agent.id}')">
                            ${createIcon('close', 12)}
                        </button>
                    </div>
                `).join('');
            }
        }
    }

    terminateAgent(agentId) {
        this.activeAgents.delete(agentId);
        this.updateAgentDisplay();
    }

    terminateAllAgents() {
        this.activeAgents.clear();
        this.updateAgentDisplay();
    }

    notifyAgentCompletion(agent, result) {
        this.addMessage('assistant', `🤖 Agent "${agent.type}" completed: ${result.message}`);

        if (result.code) {
            this.addMessage('assistant', `Generated code:\n\`\`\`${result.language || 'javascript'}\n${result.code}\n\`\`\``);
        }
    }

    startCodeAnalysis() {
        if (this.codeAnalysisInterval) {
            clearInterval(this.codeAnalysisInterval);
        }

        this.codeAnalysisInterval = setInterval(() => {
            if (this.editorManager.editor && this.codeAnalysisMode) {
                this.analyzeCurrentCode();
            }
        }, 5000); // Analyze every 5 seconds
    }

    async analyzeCurrentCode() {
        const context = this.getCurrentFileContext();
        if (!context) return;

        try {
            // Analyze code for potential issues
            const analysis = await this.performCodeAnalysis(context);

            if (analysis.suggestions.length > 0) {
                this.showCodeSuggestions(analysis.suggestions);
            }

            // Learn from code patterns
            this.learnFromCode(context, analysis);

        } catch (error) {
            console.warn('Code analysis failed:', error);
        }
    }

    async performCodeAnalysis(context) {
        // Simulate code analysis
        const suggestions = [];
        const code = context.content;

        // Check for common issues
        if (code.includes('console.log')) {
            suggestions.push({
                type: 'warning',
                message: 'Consider removing console.log statements before production',
                line: this.findLineNumber(code, 'console.log')
            });
        }

        if (code.includes('var ')) {
            suggestions.push({
                type: 'suggestion',
                message: 'Consider using let or const instead of var',
                line: this.findLineNumber(code, 'var ')
            });
        }

        return { suggestions, patterns: this.extractCodePatterns(code) };
    }

    showCodeSuggestions(suggestions) {
        if (suggestions.length === 0) return;

        const suggestionMessage = suggestions.map(s =>
            `💡 ${s.type.toUpperCase()}: ${s.message} (Line ${s.line})`
        ).join('\n');

        this.addMessage('assistant', `Code Analysis Results:\n${suggestionMessage}`);
    }

    findLineNumber(code, searchText) {
        const lines = code.split('\n');
        for (let i = 0; i < lines.length; i++) {
            if (lines[i].includes(searchText)) {
                return i + 1;
            }
        }
        return 1;
    }

    extractCodePatterns(code) {
        // Extract patterns for learning
        const patterns = {
            functions: (code.match(/function\s+\w+/g) || []).length,
            classes: (code.match(/class\s+\w+/g) || []).length,
            imports: (code.match(/import\s+.*from/g) || []).length,
            exports: (code.match(/export\s+/g) || []).length
        };
        return patterns;
    }

    learnFromCode(context, analysis) {
        // Store patterns for continuous learning
        const pattern = {
            language: context.language,
            patterns: analysis.patterns,
            timestamp: new Date(),
            file: context.name
        };

        this.codePatterns[context.language] = this.codePatterns[context.language] || [];
        this.codePatterns[context.language].push(pattern);

        // Keep only recent patterns
        if (this.codePatterns[context.language].length > 100) {
            this.codePatterns[context.language] = this.codePatterns[context.language].slice(-50);
        }

        this.saveCodePatterns();
    }

    saveCodePatterns() {
        localStorage.setItem('ai-code-patterns', JSON.stringify(this.codePatterns));
    }

    startLearningProcess() {
        // Continuous learning from user interactions
        if (this.learningInterval) {
            clearInterval(this.learningInterval);
        }

        this.learningInterval = setInterval(() => {
            this.updateUserPreferences();
        }, 30000); // Update every 30 seconds
    }

    updateUserPreferences() {
        // Analyze user behavior and update preferences
        const preferences = {
            preferredLanguages: this.getPreferredLanguages(),
            codingStyle: this.analyzeCodingStyle(),
            commonTasks: this.getCommonTasks(),
            lastUpdated: new Date()
        };

        this.userPreferences = { ...this.userPreferences, ...preferences };
        localStorage.setItem('ai-user-preferences', JSON.stringify(this.userPreferences));
    }

    getPreferredLanguages() {
        const languages = {};
        Object.keys(this.codePatterns).forEach(lang => {
            languages[lang] = this.codePatterns[lang].length;
        });
        return languages;
    }

    analyzeCodingStyle() {
        // Analyze coding style from patterns
        return {
            indentation: 'spaces', // Could be detected from code
            semicolons: true,
            quotes: 'single'
        };
    }

    getCommonTasks() {
        // Analyze conversation history for common tasks
        const tasks = {};
        this.conversationHistory.forEach(msg => {
            if (msg.type === 'user') {
                // Simple keyword analysis
                if (msg.content.includes('debug')) tasks.debug = (tasks.debug || 0) + 1;
                if (msg.content.includes('refactor')) tasks.refactor = (tasks.refactor || 0) + 1;
                if (msg.content.includes('generate')) tasks.generate = (tasks.generate || 0) + 1;
            }
        });
        return tasks;
    }

    startWorkflowMonitoring() {
        // Monitor workflow patterns and suggest automation
        if (this.workflowInterval) {
            clearInterval(this.workflowInterval);
        }

        this.workflowInterval = setInterval(() => {
            this.analyzeWorkflowPatterns();
        }, 60000); // Analyze every minute
    }

    analyzeWorkflowPatterns() {
        // Analyze user workflow and suggest improvements
        const workflow = this.getCurrentWorkflow();
        if (workflow.repetitiveActions.length > 0) {
            this.suggestWorkflowAutomation(workflow.repetitiveActions);
        }
    }

    getCurrentWorkflow() {
        // Simulate workflow analysis
        return {
            repetitiveActions: [],
            timeSpent: {},
            efficiency: 0.8
        };
    }

    suggestWorkflowAutomation(actions) {
        const suggestion = `🔄 Workflow Automation Suggestion: I noticed you're doing repetitive tasks. Would you like me to automate: ${actions.join(', ')}?`;
        this.addMessage('assistant', suggestion);
    }

    updateContextMemory(type, data) {
        this.contextMemory.push({
            type: type,
            data: data,
            timestamp: new Date()
        });

        // Keep memory size manageable
        if (this.contextMemory.length > 1000) {
            this.contextMemory = this.contextMemory.slice(-500);
        }

        localStorage.setItem('ai-context-memory', JSON.stringify(this.contextMemory));
    }

    getProjectData() {
        // Get project-specific data
        const app = window.rustCodeApp;
        if (app && app.projectManager) {
            return app.projectManager.currentProject;
        }
        return null;
    }

    // Agent task implementations
    async generateCode(task) {
        return {
            message: 'Code generated successfully',
            code: '// Generated code placeholder\nconsole.log("Hello, World!");',
            language: 'javascript'
        };
    }

    async reviewCode(task) {
        return {
            message: 'Code review completed',
            issues: ['Consider adding error handling', 'Variable naming could be improved'],
            score: 8.5
        };
    }

    async debugCode(task) {
        return {
            message: 'Debug analysis completed',
            issues: ['Potential null pointer exception on line 15'],
            suggestions: ['Add null check before accessing object properties']
        };
    }

    async generateTests(task) {
        return {
            message: 'Test cases generated',
            code: '// Generated test cases\ndescribe("Test Suite", () => {\n  it("should work", () => {\n    expect(true).toBe(true);\n  });\n});',
            language: 'javascript'
        };
    }

    async refactorCode(task) {
        return {
            message: 'Refactoring suggestions generated',
            suggestions: ['Extract method for repeated code', 'Use more descriptive variable names'],
            estimatedImprovement: '15% better readability'
        };
    }

    async generateDocumentation(task) {
        return {
            message: 'Documentation generated',
            documentation: '/**\n * Function description\n * @param {string} param - Parameter description\n * @returns {boolean} Return value description\n */',
            format: 'JSDoc'
        };
    }

    async optimizeCode(task) {
        return {
            message: 'Code optimization completed',
            optimizations: ['Reduced time complexity from O(n²) to O(n)', 'Eliminated unnecessary loops'],
            performanceGain: '40% faster execution'
        };
    }

    async analyzeSecurityIssues(task) {
        return {
            message: 'Security analysis completed',
            vulnerabilities: ['Potential XSS vulnerability in user input handling'],
            recommendations: ['Sanitize user input', 'Use parameterized queries'],
            riskLevel: 'medium'
        };
    }
}
